import { request } from "../request";
import { generateAPIPath } from "../utilities";

export interface FunctionList {
  name: string;
  type?: string; // Data
  label?: string; // Data
  identifier?: string; // Data
  data_type?:
    | "Bool"
    | "Value"
    | "Enum"
    | "Raw"
    | "String"
    | "Group Break"
    | "Tab Break"; // Bool|Value|Enum|Raw|String|Group Break|Tab Break
  icon_url?: string; // Data
  data_on_text?: string; // Data
  data_off_text?: string; // Data
  enum_value?: string; // Data
  unit?: string; // Data
  data_permission?: "r" | "rw" | "w"; // r|rw|w
  description?: string; // Data
  device_profile_id?: string; // Link
  data_measure_max?: number; // Data
  data_measure_min?: number; // Data
  data_eligible_max?: number; // Data
  data_eligible_min?: number; // Data
  chart_type?: "" | "Line" | "Text" | "Text_Line" | "Gauge" | "Card" | "Custom"; // |Line|Text|Text_Line|Gauge|Card|Custom
  round_type?: "" | "Raw" | "Round" | "Float_2" | "Float_3"; // |Raw|Round|Float_2|Float_3
  index_sort: string | number;
  md_size?: string | number;
  show_chart?: string; // Check
  function_list: any;
  device_id_thingsboard?: string;
  latest_value?: any;
}

export interface LatestDaum {
  key: string;
  ts: number;
  value: any;
}

export interface I_IOTDevice {
  name: string;
  id?: string;
  create_time: any;
  customer_id: string;
  device_profile_id: string;
  type?: string;
  label: string;
  title?: string;
  firmware_id: any;
  software_id: any;
  is_gateway: number;
  device_id_thingsboard: string;
  access_token_thingsboard?: string;
  device_profile: string;
  zone_id: string;
  zone_label: string;
  customer_name: string;
  image?: string;
  description: any;
  serial_number?: string;
  index: string;
  sort_index: string;
  project_id: string;
  project_label: string;
  function_list: FunctionList[];
  device_profile_image?: string;
  latest_data: LatestDaum[];
  IP: string;
  online: boolean;
  lastDisconnectTime: number;
  lastConnectTime: number;
}

export async function deviceInProjectList({
  page = 1,
  size = 20,
  fields = ["*"],
  filters = [],
  or_filters = [],
  order_by = "",
  group_by = "",
  project_id = "",
}: {
  page?: number;
  size?: number;
  fields?: string[];
  filters?: any;
  or_filters?: any;
  order_by?: string;
  group_by?: string;
  project_id?: string;
}): Promise<I_IOTDevice[]> {
  try {
    const params = {
      page,
      size,
      fields: JSON.stringify(fields),
      filters: JSON.stringify(filters),
      or_filters: JSON.stringify(or_filters),
      projectId: project_id,
      // order_by,
      // group_by
    };
    const queryString = new URLSearchParams(params as any).toString();
    const result = await request(
      generateAPIPath(`api/v2/device/all-in-project/`),
      {
        method: "GET",
        params: params,
        queryParams: params,
      }
    );

    return (result.responseData.result as I_IOTDevice[]).map(
      (item: I_IOTDevice) => ({
        ...item,
        function_list: item.function_list.map((fn) => ({
          ...fn,
          data_eligible_max:
            fn.data_eligible_max && typeof fn.data_eligible_max === "string"
              ? parseInt(fn.data_eligible_max)
              : fn.data_eligible_max,
          data_eligible_min:
            fn.data_eligible_min && typeof fn.data_eligible_min === "string"
              ? parseInt(fn.data_eligible_min)
              : fn.data_eligible_min,
          data_measure_min:
            fn.data_measure_min && typeof fn.data_measure_min === "string"
              ? parseInt(fn.data_measure_min)
              : fn.data_measure_min,
          data_measure_max:
            fn.data_measure_max && typeof fn.data_measure_max === "string"
              ? parseInt(fn.data_measure_max)
              : fn.data_measure_max,
        })),
      })
    );
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function projectList({
  page = 0,
  size = 20,
  fields = ["*"],
  filters = [],
  or_filters = [],
  order_by = "",
  group_by = "",
}: {
  page?: number;
  size?: number;
  fields?: string[];
  filters?: any;
  or_filters?: any;
  order_by?: string;
  group_by?: string;
}): Promise<any> {
  try {
    const params = {
      page,
      size,
      fields: JSON.stringify(fields),
      filters: JSON.stringify(filters),
      or_filters: JSON.stringify(or_filters),
    };
    const result = await request(generateAPIPath(`api/v2/assets/project`), {
      method: "GET",
      params: params,
      queryParams: params,
    });
    return result.responseData.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function controlDevice({
  device_id_thingsboard,
  method = "set_state",
  params = {},
}: {
  device_id_thingsboard: string;
  method: string;
  params: any;
}) {
  try {
    const result = await request(
      generateAPIPath(`api/v2/thingsboard/rpc/oneway/${device_id_thingsboard}`),
      {
        method: "POST",
        data: {
          method,
          params,
          timeout: 10000,
        },
      }
    );
    return result.responseData.result;
  } catch (error) {
    throw error;
  }
}

export interface ILatestDeviceTimeSeries {
  [key: string]: {
    ts: number;
    value: any;
  }[];
}

export const getLatestDataDevices = async ({
  deviceId,
  keys,
}: {
  deviceId: string;
  keys: string[];
}) => {
  console.log("Getting latest data of deviceId: ", deviceId);
  console.log("with keys: ", keys);
  const res = await request(
    // <
    //   API.ResponseResult<{
    //     data: ILatestDeviceTimeSeries;
    //   }>
    // >
    generateAPIPath(
      `api/v2/thingsboard/device-timeseries-latest/${deviceId}?keys=${keys.join(
        ","
      )}`
    ),
    {
      method: "GET",
      // params: {
      //   keys: keys ? keys.join(",") : undefined,
      // },
    }
  );

  return res.responseData.result;
};

export async function getDataTimeSeries({
  keys,
  startTs,
  endTs,
  agg = "NONE",
  device_id_thingsboard,
  limit = 100,
  interval,
}: {
  keys: string;
  startTs: number;
  endTs: number;
  agg: string;
  device_id_thingsboard: string;
  limit: number;
  interval?: number;
}): Promise<{
  [key: string]: { ts: number; value: string }[];
}> {
  try {
    console.log(
      "Getting data time series of deviceId: ",
      device_id_thingsboard
    );
    console.log("with keys: ", keys);
    console.log("from: ", new Date(startTs));
    console.log("to: ", new Date(endTs));
    console.log("agg: ", agg);
    console.log("limit: ", limit);
    console.log("interval: ", interval);
    const result = await request(
      generateAPIPath(
        `api/v2/thingsboard/get-device-timeseries-history/${device_id_thingsboard}?keys=${keys}&startTs=${startTs}&endTs=${endTs}&agg=${agg}&limit=${limit}${
          interval ? "&interval=" + interval : ""
        }`
      ),
      // {
      //   method: 'GET',
      //   params: {
      //     keys,
      //     startTs: 1694710800000,
      //     endTs: 1697328000000,
      //     agg: 'AVG',
      //     limit: 200,
      //     interval: 6000*60*24,
      //   },
      // },
      {
        method: "GET",
        // params: {
        //   keys,
        //   startTs,
        //   endTs,
        //   agg,
        //   limit,
        //   interval,
        // },
      }
    );
    return result.responseData.result;
  } catch (error) {
    throw error;
  }
}

export async function unlinkDevice(deviceId: string) {
  try {
    const result = await request(
      generateAPIPath(`api/v2/device/unlink/${deviceId}`),
      {
        method: "DELETE",
      }
    );
    return result.responseData.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
