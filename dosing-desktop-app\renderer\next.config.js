/** @type {import('next').NextConfig} */

const withTM = require("next-transpile-modules")([
  "rc-util",
  "rc-pagination",
  "rc-picker",
  "antd",
  "@ant-design/icons",
  "rc-tree",
  "rc-table",
  "rc-input",
]);

module.exports = withTM({
  output: "export",
  distDir: process.env.NODE_ENV === "production" ? "../app" : ".next",
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
  webpack: (config) => {
    return config;
  },
});
