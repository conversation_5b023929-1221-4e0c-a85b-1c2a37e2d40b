import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

export interface ProgramRunningFromBroker {
  scheduleId: string;
  label: string;
  status: string;
  start_time: string;
  end_time: string;
  timestamp: number;
}

interface ProgramRunningState {
  programRunning: ProgramRunningFromBroker[];
  setProgramRunning: (program: ProgramRunningFromBroker[]) => void;
}

const useProgramRunningStore = create(
  immer<ProgramRunningState>((set, get) => ({
    programRunning: [],
    setProgramRunning: (programRunning: ProgramRunningFromBroker[]) =>
      set({ programRunning: programRunning }),
  }))
);
export default useProgramRunningStore;
