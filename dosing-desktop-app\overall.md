# Dosing Desktop Application Overview

## Application Overview

This is an Electron-based desktop application built with Next.js (Nextron) for monitoring and controlling dosing systems. The application features real-time data visualization, device management, and scheduling capabilities, primarily targeting agricultural or industrial dosing systems.

## Core Functionality

1. **Real-time Monitoring**
   - Live data visualization using uPlot charts
   - Sensor data monitoring (EC, pH, temperature, etc.)
   - Device status and metrics display

2. **Device Control**
   - MQTT-based device communication
   - Manual control of dosing systems
   - Device calibration interfaces

3. **Scheduling System**
   - Programmable dosing schedules
   - Multiple schedule plans
   - Automated operation management

4. **User Management**
   - Authentication system
   - User session management
   - Access control

## Project Structure

## Detailed Project Structure

### Main Directories

1. **main/** - Electron main process code
   - `background.ts` - Main process entry point
   - `preload.ts` - Preload script for secure context isolation
   - `helpers/` - Utility functions for the main process

2. **renderer/** - Next.js renderer process (React application)
   - `pages/` - Application routes and pages
     - `vietplants/` - Main application pages
     - `user/` - User-related pages
     - `_app.tsx` - Main application component
   - `components/` - Reusable React components
   - `services/` - Business logic and API services
     - `auth.ts` - Authentication service
     - `device/` - Device management services
     - `schedule/` - Scheduling functionality
     - `utilities.ts` - Helper functions
   - `stores/` - State management (using Zustand)
   - `utils/` - Utility functions
   - `locales/` - Internationalization files
   - `public/` - Static assets
   - `apis/` - API integration layer

3. **resources/** - Application resources (icons, assets, etc.)

## Key Components

### Pages (`/renderer/pages/vietplants`)

- **home.tsx** - Main dashboard with real-time monitoring and controls
- **control.tsx** - Manual device control interface
- **calibsensors.tsx** - Sensor calibration interface
- **schedule.tsx** - Schedule management interface
- **schedule_plan.tsx** - Schedule plan configuration
- **schedule_program.tsx** - Program scheduling interface
- **setting.tsx** - Application settings
- **authentication.tsx** - User authentication

### Components (`/renderer/components`)

- **control/** - Control-related UI components
- **monitor/** - Monitoring and visualization components
  - `LineChart.tsx` - Reusable chart component for data visualization
- **virtual-input/** - Input components for the application

### State Management (`/renderer/stores`)

- **mqttStore.ts** - Manages MQTT connections and subscriptions
- **deviceDataStore.ts** - Stores and manages device data
- **userStore.ts** - Manages user authentication state
- **languageStore.ts** - Handles application localization

### Services (`/renderer/services`)
- **device/** - Device management services
  - `devices.ts` - Device-related API calls and logic
  - `useControlDevice.ts` - Custom hook for device control
- **auth.ts** - Authentication services
- **request.ts** - API request utilities
- **utilities.ts** - Helper functions

### MQTT Integration

The application uses MQTT for real-time communication with devices:
- Secure connection with token-based authentication
- Topic-based subscription system
- Automatic reconnection handling
- Message queuing for offline scenarios

### Data Flow

1. **Data Collection**
   - Devices publish data via MQTT topics
   - MQTT client in the renderer process subscribes to relevant topics
   - Incoming messages are processed and stored in the appropriate stores

2. **State Management**
   - Zustand stores maintain application state
   - Components subscribe to store updates
   - UI re-renders when relevant data changes

3. **User Interaction**
   - User actions trigger API calls or MQTT messages
   - State updates trigger UI updates
   - Feedback is provided for user actions

## Development Notes

- The application uses TypeScript for type safety
- State management is handled by Zustand with Immer for immutable updates
- UI components are built with Ant Design
- Real-time charts are implemented using uPlot
- The codebase follows a modular architecture with clear separation of concerns

## Testing

The application includes unit tests for critical components and utilities. Test files are located alongside the corresponding source files with `.test.ts` or `.spec.ts` extensions.

## Environment Variables

- `NEXT_PUBLIC_API_URL` - Base URL for API requests
- `NEXT_PUBLIC_MQTT_BROKER` - MQTT broker URL
- `NEXT_PUBLIC_ENV` - Environment (development/production)

## Build and Deployment

The application can be built for multiple platforms using electron-builder. The build process packages the Next.js frontend with the Electron wrapper to create a standalone desktop application.

## Known Issues

- Some components use mock data for demonstration purposes
- Error handling could be more comprehensive
- Some UI components may need optimization for better performance

## Future Improvements

- Implement more comprehensive error handling and recovery
- Add more comprehensive testing
- Improve performance for large datasets
- Add more configuration options for advanced users
- Implement data export/import functionality
- Add support for plugins/extensions

## Dependencies

### Main Dependencies
- React 18 - UI library
- Next.js 14 - React framework
- Electron - Desktop application framework
- Zustand - State management
- Ant Design - UI component library
- MQTT.js - MQTT client
- uPlot - High-performance charting library
- Immer - Immutable state updates

### Development Dependencies
- TypeScript - Type checking
- Jest - Testing framework
- ESLint - Code linting
- Prettier - Code formatting
- electron-builder - Application packaging

## License

Proprietary - All rights reserved

## Technology Stack

- **Frontend Framework**: Next.js 14 with React 18
- **Desktop Runtime**: Electron
- **State Management**: Zustand with Immer
- **UI Components**: Ant Design
- **Data Visualization**: uPlot
- **Real-time Communication**: MQTT.js
- **HTTP Client**: umi-request
- **Data Persistence**: electron-store
- **Internationalization**: i18next
- **Styling**: CSS Modules with Less
- **Build Tool**: electron-builder
- **Package Manager**: npm
- **Language**: TypeScript

## Architecture Overview

The application follows the Electron architecture with a clear separation between:

1. **Main Process**
   - Handles application lifecycle
   - Manages native OS interactions
   - Creates application windows
   - Handles system-level operations

2. **Renderer Process**
   - Next.js-based React application
   - Handles UI rendering
   - Manages application state
   - Communicates with main process via IPC
   - Handles MQTT communication

3. **Preload Script**
   - Bridges main and renderer processes
   - Exposes safe, whitelisted APIs
   - Implements security best practices

4. **Backend Services**
   - MQTT broker for real-time communication
   - RESTful API for data management
   - Authentication service

## Security Considerations

- Secure token-based authentication
- Context isolation enabled
- Content Security Policy (CSP) implemented
- Secure handling of sensitive data
- Input validation and sanitization
- Secure communication protocols (MQTT over TLS/SSL)

- **Framework**: Nextron (Next.js + Electron)
- **UI Library**: React 18
- **State Management**: Zustand
- **UI Components**: Ant Design
- **Charts**: uPlot
- **MQTT**: mqtt.js for real-time communication
- **Data Persistence**: electron-store
- **HTTP Client**: umi-request
- **Build Tool**: electron-builder

## Key Features

1. **Authentication**
   - User login/logout
   - Session management

2. **Device Management**
   - MQTT-based device communication
   - Real-time monitoring and control

3. **Scheduling**
   - Task scheduling functionality
   - Automated operations

4. **Data Visualization**
   - Real-time charts using uPlot
   - Data monitoring and analysis

## Development Scripts

- `npm run dev` - Start development server
- `npm run build` - Build application for production
- Platform-specific builds:
  - `build:mac` - Build for macOS
  - `build:win64` - Build for Windows 64-bit
  - `build:linux` - Build for Linux

## Project Dependencies

### Main Dependencies
- React 18
- Next.js 14
- Electron
- Zustand (state management)
- Ant Design (UI components)
- MQTT.js (real-time communication)
- uPlot (data visualization)

### Development Dependencies
- TypeScript
- ESLint
- Prettier
- Jest (for testing)

## Architecture

The application follows the Electron architecture with a clear separation between:

1. **Main Process**
   - Handles application lifecycle
   - Manages native OS interactions
   - Creates application windows

2. **Renderer Process**
   - Next.js-based React application
   - Handles UI rendering
   - Communicates with main process via IPC

3. **Preload Script**
   - Bridges main and renderer processes
   - Exposes safe, whitelisted APIs

## Build Configuration

The application uses electron-builder for packaging and distribution, with configurations for multiple platforms including Windows, macOS, and Linux.
