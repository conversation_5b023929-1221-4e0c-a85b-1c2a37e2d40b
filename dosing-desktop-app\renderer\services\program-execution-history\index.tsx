import { request } from "../request";
import { generateAPIPath } from "../utilities";

export interface Root {
  result: Result;
}

export interface Result {
  data: ProgramExecutionHistoryType[];
  pagination: Pagination;
}

export interface ProgramExecutionHistoryType {
  name: string;
  schedule_id: string;
  label: string;
  device_id: string;
  start_date: string; // Ví dụ: "2025-04-10"
  end_date: string; // Ví dụ: "2025-12-31"
  start_time: string; // Ví dụ: "15:20:00"
  end_time: string; // Ví dụ: "15:24:00"
  start_time_unix: string; // Ví dụ: "2025-06-06T15:20:00.000Z"
  end_time_unix: string; // Ví dụ: "2025-06-06T15:24:00.000Z"
  log_creation: string; // Ví dụ: "2025-06-06 15:20:01"
  log_modified: string; // Ví dụ: "2025-06-06 15:20:01"
  notifications: Notification[];
  errors: any[]; // Ví dụ: mảng rỗng
  warnings: Notification[];
  avg_device_data?: any;
  data_by_key?: Record<string, DataPoint[]>;
}

export interface Notification {
  name: string; // Ví dụ: "18eaa0cf-6229-4d5a-b391-e92b2d555874"
  message: string; // Ví dụ: "Bơm chính được bật từ Tủ Golden Bees"
  severity: string; // Ví dụ: "" hoặc "notification"
  created_at: string; // Ví dụ: "2025-06-06 15:20:05"
  type: string; // Ví dụ: "device"
}

export interface DataPoint {
  timestamp: number; // Ví dụ: 1749198002781
  value: string; // Ví dụ: "130" hoặc "false" hoặc "{\"value\":33.2925,...}"
}

export interface Pagination {
  totalElements: number; // Ví dụ: 1686
  totalPages: number; // Ví dụ: 1686
  pageSize: number; // Ví dụ: 1
  pageNumber: number; // Ví dụ: 1
  order_by: string; // Ví dụ: "tabiot_schedule_plan.label ASC"
}

interface Customer {
  name: string;
  email?: string;
  first_name?: string;
  last_name?: string;
}

export const getProgramExecutionHistory = async (params?: {
  filters?: any;
}) => {
  const response = await request(generateAPIPath("api/v2/scheduleLog"), {
    method: "GET",
    params: params,
  });
  return response.responseData;
};
