import mqtt, { MqttClient } from "mqtt";
import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { MqttDeviceEventEmitterControl } from "../events/mqtt/mqtt-device-eventemitter";
import { MqttNoticeEventEmitterControl } from "../events/mqtt/mqtt-notice-eventemitter";
import { genNoticeReadAllTopic, genNoticeUpdateTopic } from "../utils/mqtt";
import { getUserIdFromToken } from "../utils/localStorage";
import { MqttNoticeEventReadAllControl } from "../events/mqtt/mqtt-notice-read-all";

interface Subscription {
  id: string;
  callback: (message: string) => void;
}

interface MqttState {
  client: MqttClient | null;
  connected: boolean;
  subscriptions: { [topic: string]: Subscription[] };
  connect: (brokerUrl: string) => void;
  disconnect: () => void;
  subscribe: (
    topics: string[],
    callback: (message: string) => void
  ) => string[];
  unsubscribe: (ids: string[]) => void;
  handleMessage: {
    deviceHandle: {
      receive: any;
      emit: Record<string, (...args: any[]) => void>;
    };
    noticeHandle: {
      receive: any;
      emit: {
        updateNoticeTask: (message: {
          name: string;
          message: string;
          created_at: string;
          entity: string;
          type: "task";
          is_read: boolean;
        }) => void;
      };
    };
    noticeHandleReadAll: {
      receive: any;
      emit: {
        readAll: (message: { isRead: boolean; time: number }) => void;
      };
    };
  };
}

const getOptions = () => {
  const token = localStorage.getItem("token");
  const userData = token ? JSON.parse(token) : {};
  const options = {
    clean: true, // Retain session
    connectTimeout: 4000, // Timeout period
    clientId: Math.random().toString(16).slice(2), // Generate a random client ID
    username: "unused",
    password: userData?.accessToken || "",
    keepalive: 300,
    reconnectPeriod: 1000, // Time between reconnection attempts (ms)
  };
  return options;
};

const useMqttStore = create(
  immer<MqttState>((set, get) => ({
    client: null,
    connected: false,
    subscriptions: {},

    /**
     * Disconnect from the MQTT broker and clear subscriptions.
     */
    disconnect: () => {
      const { client } = get();
      if (client) {
        client.end();
        set({ client: null, subscriptions: {} });
      }
    },

    /**
     * Connect to the MQTT broker with the given URL.
     * @param brokerUrl The URL of the MQTT broker.
     */
    connect: (brokerUrl: string) => {
      console.log("brokerUrl: ", brokerUrl);
      if (get().client) return;
      const client = mqtt.connect(brokerUrl, {
        ...getOptions(),
      });

      client.on("connect", () => {
        set({ connected: true });
        // message.success('Connected');
        // console.log('Connected to broker');
      });

      client.on("reconnect", () => {
        console.log("Reconnecting to broker...");
      });

      client.on("message", (topic: string, message: Buffer) => {
        const subscriptions = get().subscriptions;
        if (subscriptions[topic]) {
          subscriptions[topic].forEach((sub) => {
            try {
              sub.callback(message.toString());
            } catch (error) {
              console.log("error: ", error);
            }
          });
        }
      });
      // setInterval(()=>{
      //    const subscriptions = get().subscriptions;
      //      Object.values(subscriptions).flat(1).forEach((sub) => {
      //        try {
      //          sub.callback(
      //            JSON.stringify([
      //              {
      //                ts: new Date().getTime(),
      //                key: 'flowNegativeRate',
      //                value: Math.floor(Math.random() * 100),
      //              },
      //            ]),
      //          );
      //        } catch (error) {
      //          console.log('error: ', error);
      //        }
      //      });
      // },2000)

      client.on("offline", () => {
        set({ connected: false });
        console.log("Client is offline");
      });

      client.on("error", (error) => {
        console.error("Connection error:", error);
      });

      client.on("disconnect", () => {
        set({ connected: false });
        console.log("Disconnected from broker");
      });

      set({ client });
    },

    /**
     * Subscribe to the given topics with a callback function.
     * @param topics An array of topics to subscribe to.
     * @param callback The callback function to call when a message is received on the subscribed topics.
     * @returns An array of subscription IDs.
     */
    subscribe: (topics: string[], callback: (message: string) => void) => {
      const { client, subscriptions } = get();
      const id = Math.random().toString(16).slice(2); // Generate a unique ID for the subscription

      if (client) {
        topics.forEach((topic) => {
          if (!subscriptions[topic]) {
            client.subscribe(topic, (err) => {
              if (!err) {
                set((state) => {
                  if (!state.subscriptions[topic]) {
                    state.subscriptions[topic] = [];
                  }
                  state.subscriptions[topic].push({ id, callback });
                });
                console.log(`Subscribed to ${topic}`);
              }
            });
          } else {
            set((state) => {
              state.subscriptions[topic].push({ id, callback });
            });
          }
        });

        return [id];
      }
      return [];
    },

    /**
     * Unsubscribe from the topics associated with the given subscription IDs.
     * @param ids An array of subscription IDs to unsubscribe from.
     */
    unsubscribe: (ids: string[]) => {
      const { client, subscriptions } = get();
      if (client) {
        Object.keys(subscriptions).forEach((topic) => {
          // Filter out the subscriptions with the given IDs
          const newSubscriptions = subscriptions[topic].filter(
            (sub) => !ids.includes(sub.id)
          );

          if (newSubscriptions.length === 0) {
            // If no subscriptions remain for this topic, unsubscribe from the topic
            client.unsubscribe(topic, (err: any) => {
              if (!err) {
                set((state) => {
                  delete state.subscriptions[topic];
                });
                console.log(`Unsubscribed from ${topic}`);
              }
            });
          } else {
            // Otherwise, update the subscriptions for this topic
            set((state) => {
              state.subscriptions[topic] = newSubscriptions;
            });
            console.log(`Unsubscribed from - event ${topic}`);
          }
        });
      }
    },

    handleMessage: {
      deviceHandle: {
        receive: new MqttDeviceEventEmitterControl(),
        emit: {},
      },
      noticeHandle: {
        receive: new MqttNoticeEventEmitterControl(),
        emit: {
          updateNoticeTask: (message) => {
            const { client } = get();
            if (client) {
              client.publish(
                genNoticeUpdateTopic(),
                JSON.stringify({
                  ...message,
                  is_read: true,
                  customer_user: getUserIdFromToken(),
                }),
                (err) => {
                  if (err) console.log("err: ", err);
                }
              );
            }
          },
        },
      },
      noticeHandleReadAll: {
        receive: new MqttNoticeEventReadAllControl(),
        emit: {
          readAll: (message) => {
            const { client } = get();
            if (client) {
              client.publish(
                genNoticeReadAllTopic(),
                JSON.stringify(message),
                (err) => {
                  if (err) console.log("err: ", err);
                }
              );
            }
          },
        },
      },
    },
  }))
);
export const mqttStoreSelector = {
  client: (state: MqttState) => state.client,
  connected: (state: MqttState) => state.connected,
  subscriptions: (state: MqttState) => state.subscriptions,
  subscribe: (state: MqttState) => state.subscribe,
  unsubscribe: (state: MqttState) => state.unsubscribe,
  disconnect: (state: MqttState) => state.disconnect,
  connect: (state: MqttState) => state.connect,
  handleMessage: (state: MqttState) => state.handleMessage,
};
export { useMqttStore };
