import { PoweroffOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Image, Row, Select, Space } from "antd";
import React, { CSSProperties, FC, useEffect, useRef, useState } from "react";
import { mqttStoreSelector, useMqttStore } from "../../stores/mqttStore";
import useDeviceDataStore from "../../stores/deviceDataStore";
import {
  FunctionList,
  getLatestDataDevices,
} from "../../services/device/devices";
import MonitorDevice from "../../components/monitor";
import LineChart from "../../components/monitor/LineChart";
import OnOffControl from "../../components/control/OnOffControl";
import useDeviceEcPhTempStore from "../../stores/deviceEcPhTemp";
import { genDeviceTopic } from "../../stores/mqttStore.utils";
import AnimateValveT1 from "../../elements/vietplants/monitor/AnimateValveT1";
// import { getsched } from "../../services/schedule";

interface AnimateValveProps {
  valve: any;
  direction: "horizontal" | "vertical";
  absolutePosition: CSSProperties;
  setDemoValveStatus: (valveStatus: any) => void;
}

interface AnimateValveFromTankMixerProps {
  valve: any;
  absolutePosition: CSSProperties;
  setDemoValveStatusFromTankMixer: (valveStatus: any) => void;
}

interface AnimatePumpProps {
  pump: any;
  absolutePosition: CSSProperties;
  setDemoPumpStatus: (pumpStatus: any) => void;
}

const AnimateValve: FC<AnimateValveProps> = ({
  valve,
  direction,
  absolutePosition,
  setDemoValveStatus,
}) => {
  if (direction === "vertical") {
    return (
      <div
        style={{
          position: "absolute",
          cursor: "pointer",
          zIndex: 100,
          ...absolutePosition,
        }}
        onClick={() => {
          setDemoValveStatus((prev) => ({
            ...prev,
            [valve.id]: !prev[valve.id],
          }));
        }}
      >
        <div style={{ position: "relative" }}>
          <p
            style={{
              position: "absolute",
              top: 52,
              left: 8,
              fontSize: 12,
              color: valve.enable === true ? "#1200DC" : "#797979",
              fontWeight: valve.enable === true ? "bold" : "normal",
            }}
          >
            {valve.id.slice(6)}
          </p>
          <div
            className={`${valve.enable === true ? "route-pipeline" : ""}`}
            style={{
              position: "absolute",
              transform: "rotate(-90deg)",
              top: 8,
              left: -4,
              height: "4px",
              width: "20px",
              backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
            }}
          ></div>
          <img
            src={
              valve.enable === true
                ? "/scada/valve-open.svg"
                : "/scada/valve-close.svg"
            }
            style={{
              position: "absolute",
              left: 0,
              top: 20,
              height: "30px",
              width: "30px",
              transform: "rotate(90deg)",
            }}
          />
          <div
            className={`${valve.enable === true ? "route-pipeline" : ""}`}
            style={{
              position: "absolute",
              transform: "rotate(-90deg)",
              left: -4,
              top: 58,
              height: "4px",
              width: "20px",
              backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
            }}
          ></div>
        </div>
      </div>
    );
  } else {
    return (
      <div
        style={{
          position: "absolute",
          cursor: "pointer",
          zIndex: 100,
          ...absolutePosition,
        }}
        onClick={() => {
          setDemoValveStatus((prev) => ({
            ...prev,
            [valve.id]: !prev[valve.id],
          }));
        }}
      >
        <div style={{ position: "relative" }}>
          <div
            className={`${valve.enable === true ? "route-pipeline" : ""}`}
            style={{
              position: "absolute",
              transform: "rotate(180deg)",
              top: 22,
              left: 0,
              height: "4px",
              width: "30px",
              backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
            }}
          ></div>
          <img
            src={
              valve.enable === true
                ? "/scada/valve-open.svg"
                : "/scada/valve-close.svg"
            }
            style={{
              position: "absolute",
              left: 30,
              top: 0,
              height: "30px",
              width: "30px",
            }}
          />
          <div
            className={`${valve.enable === true ? "route-pipeline" : ""}`}
            style={{
              position: "absolute",
              left: 60,
              top: 22,
              transform: "rotate(180deg)",
              height: "4px",
              width: "34px",
              backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
            }}
          ></div>
        </div>
      </div>
    );
  }
};

const AnimateValveFromTankMixer: FC<AnimateValveFromTankMixerProps> = ({
  valve,
  absolutePosition,
  setDemoValveStatusFromTankMixer,
}) => {
  return (
    <div
      style={{
        position: "absolute",
        cursor: "pointer",
        zIndex: 100,
        ...absolutePosition,
      }}
      onClick={() => {
        setDemoValveStatusFromTankMixer((prev) => ({
          ...prev,
          [valve.id]: !prev[valve.id],
        }));
      }}
    >
      <div style={{ position: "relative" }}>
        <div
          className={`${valve.enable === true ? "route-pipeline" : ""}`}
          style={{
            position: "absolute",
            top: 6,
            transform: "rotate(-90deg)",
            left: -2,
            height: "4px",
            width: "15px",
            backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
          }}
        ></div>
        <img
          src={
            valve.enable === true
              ? "/scada/valve-open.svg"
              : "/scada/valve-close.svg"
          }
          style={{
            position: "absolute",
            left: 0,
            top: 15,
            height: "30px",
            width: "30px",
            transform: "rotate(90deg)",
          }}
        />
        <div
          className={`${valve.enable === true ? "route-pipeline" : ""}`}
          style={{
            position: "absolute",
            left: -2,
            top: 51,
            transform: "rotate(-90deg)",
            height: "4px",
            width: "16px",
            backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
          }}
        ></div>
      </div>
    </div>
  );
};

const AnimatePump: FC<AnimatePumpProps> = ({
  pump,
  absolutePosition,
  setDemoPumpStatus,
}) => {
  return (
    <div
      style={{
        position: "absolute",
        cursor: "pointer",
        zIndex: 100,
        ...absolutePosition,
      }}
      onClick={() => {
        setDemoPumpStatus((prev) => ({
          ...prev,
          [pump.id]: !prev[pump.id],
        }));
      }}
    >
      <div style={{ position: "relative" }}>
        <div
          className={`${pump.enable === true ? "route-pipeline" : ""}`}
          style={{
            position: "absolute",
            top: 13,
            left: 0,
            transform: "rotate(180deg)",
            height: "4px",
            width: "22px",
            backgroundColor: pump.enable === true ? "#1200DC" : "#797979",
          }}
        ></div>
        <img
          src={
            pump.enable === true ? "/scada/pump-on.svg" : "/scada/pump-off.svg"
          }
          style={{
            position: "absolute",
            left: 20,
            top: 0,
            height: "30px",
            width: "30px",
          }}
        />
        <div
          className={`${pump.enable === true ? "route-pipeline" : ""}`}
          style={{
            position: "absolute",
            left: 50,
            top: 13,
            transform: "rotate(180deg)",
            height: "4px",
            width: "18px",
            backgroundColor: pump.enable === true ? "#1200DC" : "#797979",
          }}
        ></div>
      </div>
    </div>
  );
};

export default function HomePage() {
  const { functionListForMonitor, deviceId } = useDeviceDataStore();
  useEffect(() => {
    console.log(functionListForMonitor);
  }, [functionListForMonitor]);

  const [sortCompleted, setSortCompleted] = useState(false);
  useEffect(() => {
    console.log(functionListForMonitor);
    if (sortCompleted) return;
    // Sort the groups within each tab
    const newSortedList = functionListForMonitor.map((tab: any) => {
      tab.children.sort((groupA: any, groupB: any) => {
        const hasBoolA = groupA.children.some(
          (child: FunctionList) => child.data_type === "Bool"
        );
        const hasBoolB = groupB.children.some(
          (child: FunctionList) => child.data_type === "Bool"
        );

        if (hasBoolA && !hasBoolB) {
          return -1;
        }
        if (!hasBoolA && hasBoolB) {
          return 1;
        }
        return 0;
      });
      return tab;
    });

    setSortCompleted(true);
  }, [functionListForMonitor]);

  const [demoValveStatus, setDemoValveStatus] = useState({
    "valve-1": false,
    "valve-2": false,
    "valve-3": false,
    "valve-4": false,
    "valve-5": true,
    "valve-6": false,
    "valve-7": false,
    "valve-8": false,
    "valve-9": true,
    "valve-10": false,
    "valve-11": false,
    "valve-12": false,
    "valve-13": false,
  });

  const [demoValveStatusFromTankMixer, setDemoValveStatusFromTankMixer] =
    useState({
      "valve-1": false,
    });

  const [demoValveStatusToHeatingBath, setDemoValveStatusToHeatingBath] =
    useState({
      "valve-1": false,
    });

  const [demoPumpStatus, setDemoPumpStatus] = useState({
    "pump-1": false,
  });

  const { ec, ph, temp, setEc, setPh, setTemp } = useDeviceEcPhTempStore();
  const { subscribe, unsubscribe } = useMqttStore();

  useEffect(() => {
    if (!deviceId) return;

    async function getLatestDataFromHTTPAPI() {
      const keys = ["INPUT_EC", "INPUT_PH"];
      await getLatestDataDevices({
        deviceId: deviceId,
        keys: keys,
      }).then((res) => {
        console.log("latest data for EC and PH: ", res);
        const dataEC = res?.data?.INPUT_EC?.[0]?.value;
        const dataPH = res?.data?.INPUT_PH?.[0]?.value;
        if (dataEC) {
          setEc(dataEC);
        }
        if (dataPH) {
          setPh(dataPH);
        }
      });
    }
    getLatestDataFromHTTPAPI();

    // const topic = genDeviceTopic(deviceId);
    // console.log("genDeviceTopic:", topic);
    // const handleMessageMQTT = (msg: string) => {
    //   console.log("data from broker for control page:", msg);
    //   try {
    //     const data: any[] = JSON.parse(msg);
    //     if (Array.isArray(data)) {
    //       const deviceData = data.filter(
    //         (d) => d.key === "INPUT_EC" || d.key === "INPUT_PH"
    //       );
    //       if (deviceData.length > 0) {
    //         console.log(
    //           "From Broker: deviceData for INPUT_EC and INPUT_PH: ",
    //           deviceData
    //         );

    //         const latestData = deviceData[deviceData.length - 1];
    //         const valueAsNumber = parseFloat(latestData.value);
    //         if (latestData.key === "INPUT_EC") {
    //           setEc(isNaN(valueAsNumber) ? "" : valueAsNumber.toFixed(2));
    //         } else if (latestData.key === "INPUT_PH") {
    //           setPh(isNaN(valueAsNumber) ? "" : valueAsNumber.toFixed(2));
    //         }
    //       }
    //     }
    //   } catch (error) {
    //     console.error("MQTT message error:", error);
    //   }
    // };
    // const subscriptionIds = subscribe([topic], handleMessageMQTT);

    // return () => {
    //   unsubscribe(subscriptionIds);
    // };
  }, [deviceId]);

  return (
    <div
      style={{ display: "flex", flexDirection: "column", alignItems: "start" }}
    >
      <Row style={{ width: "100%" }}>
        <Col span={24}>
          <div
            style={{
              backgroundColor: "#fff",
              padding: 16,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Image src="/scada/home_vietplants_static.svg" preview={false} />
          </div>
          {/* <div
            style={{
              position: "relative",
              left: 100,
            }}
          >
            <div
              style={{ backgroundColor: "#fff", padding: 16, width: "800px" }}
            >
              <Image src="/scada/home_vietplants.svg" preview={false} />
            </div>
            <p
              style={{
                position: "absolute",
                top: 20,
                left: 56,
                color: "#45c3a1",
                fontSize: 16,
                fontWeight: "bold",
                margin: 0,
              }}
            >
              {ec}
            </p>
            <p
              style={{
                position: "absolute",
                top: 48,
                left: 56,
                color: "#45c3a1",
                fontSize: 16,
                fontWeight: "bold",
                margin: 0,
              }}
            >
              {ph}
            </p>

            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 350,
              }}
              valve={{
                id: "valve-1",
                enable: demoValveStatus["valve-1"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 380,
              }}
              valve={{
                id: "valve-2",
                enable: demoValveStatus["valve-2"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 410,
              }}
              valve={{
                id: "valve-3",
                enable: demoValveStatus["valve-3"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 440,
              }}
              valve={{
                id: "valve-4",
                enable: demoValveStatus["valve-4"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 470,
              }}
              valve={{
                id: "valve-5",
                enable: demoValveStatus["valve-5"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 500,
              }}
              valve={{
                id: "valve-6",
                enable: demoValveStatus["valve-6"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 530,
              }}
              valve={{
                id: "valve-7",
                enable: demoValveStatus["valve-7"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 560,
              }}
              valve={{
                id: "valve-8",
                enable: demoValveStatus["valve-8"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 590,
              }}
              valve={{
                id: "valve-9",
                enable: demoValveStatus["valve-9"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 620,
              }}
              valve={{
                id: "valve-10",
                enable: demoValveStatus["valve-10"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 650,
              }}
              valve={{
                id: "valve-11",
                enable: demoValveStatus["valve-11"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 680,
              }}
              valve={{
                id: "valve-12",
                enable: demoValveStatus["valve-12"],
              }}
            />
            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 710,
              }}
              valve={{
                id: "valve-13",
                enable: demoValveStatus["valve-13"],
              }}
            />

            <AnimateValveT1
              absolutePosition={{
                top: 92,
                left: 250,
              }}
              valve={{
                id: "valve-13",
                enable: demoValveStatus["valve-13"],
              }}
            />
          </div> */}
        </Col>

        <Col span={24}>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: 16,
              padding: 16,
              marginTop: 16,
            }}
          >
            {functionListForMonitor.map((tab: any) =>
              tab?.children?.length === 0 ? null : (
                <div
                  key={tab.label}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 8,
                    marginBottom: 40,
                  }}
                >
                  {tab?.children?.map((group: any) =>
                    group?.children?.length === 0 ? null : (
                      <div key={group.label} style={{ marginBottom: 40 }}>
                        <p
                          style={{
                            fontSize: 20,
                            fontWeight: "bold",
                            margin: 0,
                            marginBottom: 10,
                          }}
                        >
                          {group.label}
                        </p>
                        <Row gutter={[16, 16]}>
                          {group.children.map((child: FunctionList) => (
                            <Col key={child.label} span={12}>
                              {child.data_type === "Value" ? (
                                <MonitorDevice
                                  functionItem={child}
                                  lineColor={
                                    child.label.includes("hiệt độ")
                                      ? "orange"
                                      : child.label.includes("ec") ||
                                        child.label.includes("EC")
                                      ? "#438de0"
                                      : child.label.includes("pH") ||
                                        child.label.includes("PH")
                                      ? "#a643e0"
                                      : "#45c3a1"
                                  }
                                />
                              ) : (
                                <OnOffControl functionItem={child} readonly />
                              )}
                            </Col>
                          ))}
                        </Row>
                      </div>
                    )
                  )}
                </div>
              )
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
}
