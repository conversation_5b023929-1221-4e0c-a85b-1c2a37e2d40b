import path from "path";
import { app, ipcMain } from "electron";
import serve from "electron-serve";
import { createWindow } from "./helpers";

const isProd = process.env.NODE_ENV === "production";

if (isProd) {
  serve({ directory: "app" });
} else {
  app.setPath("userData", `${app.getPath("userData")} (development)`);
}

(async () => {
  await app.whenReady();

  const mainWindow = createWindow("main", {
    width: 1024,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
    },
    kiosk: process.env.NODE_ENV === "production",
    fullscreen: process.env.NODE_ENV === "production",
  });

  if (isProd) {
    await mainWindow.loadURL("app://./vietplants/home");
  } else {
    const port = process.argv[2];
    await mainWindow.loadURL(`http://localhost:${port}/vietplants/home`);
    // mainWindow.webContents.openDevTools();
  }
})();

app.on("window-all-closed", () => {
  app.quit();
});

ipcMain.on("message", async (event, arg) => {
  event.reply("message", `${arg} World!`);
});

ipcMain.on("close-app", () => {
  app.quit();
});

ipcMain.on("minimize-app", () => {
  const windows = require("electron").BrowserWindow.getAllWindows();
  if (windows.length > 0) {
    windows[0].minimize();
  }
});
