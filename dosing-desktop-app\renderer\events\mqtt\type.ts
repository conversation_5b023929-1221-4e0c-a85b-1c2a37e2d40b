import EventEmitter from "eventemitter3";
import { OnMessageCallback } from "mqtt";

export interface HandleRealtimeMqtt<T> {
  readonly events: EventEmitter;
  readonly keyEvent: string;
  handleReceiveMessage: OnMessageCallback;
  emit: (message: T) => void;
  on: (handle: (message: T) => void) => {
    removeSubscribe: () => void;
  };
  removeAllListeners: () => void;
}

export type NoticeDataRes = {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  customer_user: string;
  message: string;
  created_at: string;
  entity: string;
  type: "task" | "other" | "approval request" | "approval response" | "device";
  is_read: number;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
};
