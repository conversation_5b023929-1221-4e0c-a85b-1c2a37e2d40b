import { Card, Descriptions, Tag, Typography, Alert } from "antd";
import { FC } from "react";

const { Text } = Typography;

interface CalibrationDebugInfoProps {
  pumpIndex: number;
  oldCalibValue: number | null;
  latestHoldingValue: number | null;
  actualMlValue: number | null;
  isCalibrationInProgress: boolean;
  isCalibProgressFinished: boolean;
}

const CalibrationDebugInfo: FC<CalibrationDebugInfoProps> = ({
  pumpIndex,
  oldCalibValue,
  latestHoldingValue,
  actualMlValue,
  isCalibrationInProgress,
  isCalibProgressFinished,
}) => {
  // Calculate expected values for debugging
  const calculateExpectedValues = () => {
    if (!oldCalibValue || !latestHoldingValue || !actualMlValue) {
      return null;
    }

    const runTime = latestHoldingValue / oldCalibValue;
    const newCalibValue = actualMlValue / runTime;

    return {
      runTime: runTime.toFixed(4),
      newCalibValue: newCalibValue.toFixed(4),
      roundedNewCalib: Math.round(newCalibValue * 100) / 100,
    };
  };

  const expectedValues = calculateExpectedValues();

  return (
    <Card
      size="small"
      title={`Debug Info - Bơm ${pumpIndex + 1}`}
      style={{ marginTop: 16 }}
    >
      <Alert
        message="Thông tin debug"
        description="Thông tin này giúp kiểm tra quá trình tính toán hiệu chuẩn"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Descriptions size="small" column={1} bordered>
        <Descriptions.Item label="Pump Index">
          <Tag color="blue">{pumpIndex + 1}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="Keys sẽ được gửi">
          <div>
            <Text code>CALIB_ACTUAL_ML_BOM_{pumpIndex + 1}</Text>
            <br />
            <Text code>CALCULATE_CALIB_BOM_{pumpIndex + 1}</Text>
          </div>
        </Descriptions.Item>

        <Descriptions.Item label="Hệ số hiệu chuẩn cũ">
          <Text strong>
            {oldCalibValue !== null ? oldCalibValue : "Chưa có dữ liệu"}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: 11 }}>
            HOLDING_CALIB_BOM_{pumpIndex + 1}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="Giá trị SET ML hiện tại">
          <Text strong>
            {latestHoldingValue !== null
              ? `${latestHoldingValue} ml`
              : "Chưa có dữ liệu"}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: 11 }}>
            HOLDING_SETML_BOM_{pumpIndex + 1}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="Giá trị thực tế đo được">
          <Text strong style={{ color: actualMlValue ? "#52c41a" : "#ff4d4f" }}>
            {actualMlValue !== null ? `${actualMlValue} ml` : "Chưa nhập"}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="Trạng thái">
          <div>
            <Tag color={isCalibProgressFinished ? "green" : "default"}>
              {isCalibProgressFinished ? "Đã hoàn thành chạy" : "Chưa chạy"}
            </Tag>
            <Tag color={isCalibrationInProgress ? "processing" : "default"}>
              {isCalibrationInProgress ? "Đang tính toán" : "Chờ tính toán"}
            </Tag>
          </div>
        </Descriptions.Item>

        {expectedValues && (
          <>
            <Descriptions.Item label="Thời gian chạy dự kiến">
              <Text code>{expectedValues.runTime} giây</Text>
            </Descriptions.Item>

            <Descriptions.Item label="Hệ số mới dự kiến">
              <div>
                <Text code>{expectedValues.newCalibValue}</Text>
                <br />
                <Text strong>Làm tròn: {expectedValues.roundedNewCalib}</Text>
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="Công thức">
              <div>
                <Text code>
                  RUN_TIME = {latestHoldingValue} / {oldCalibValue} ={" "}
                  {expectedValues.runTime}
                </Text>
                <br />
                <Text code>
                  NEW_CALIB = {actualMlValue} / {expectedValues.runTime} ={" "}
                  {expectedValues.newCalibValue}
                </Text>
              </div>
            </Descriptions.Item>
          </>
        )}
      </Descriptions>

      {!expectedValues && (
        <Alert
          message="Chưa đủ dữ liệu để tính toán"
          description="Cần có đầy đủ: hệ số cũ, giá trị SET ML, và giá trị thực tế đo được"
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </Card>
  );
};

export default CalibrationDebugInfo;
