import useDeviceDataStore from "../../../stores/deviceDataStore";
import { useEffect, useState } from "react";
import { FunctionList } from "../../../services/device/devices";
import CalibPump from "./CalibPump";
import CalibrationGuide from "./CalibrationGuide";
import CalibrationTest from "./CalibrationTest";
import DebugControlPanel from "./DebugControlPanel";
import { Collapse } from "antd";
import {
  shouldShowDebugFeature,
  shouldShowDebug,
} from "../../../utils/debugMode";

const CalibContainer = () => {
  const { functionListForCalibration, functionListForControl } =
    useDeviceDataStore();

  const [isThereAnyCalibration, setIsThereAnyCalibration] =
    useState<null | FunctionList>(null);

  const [dataOfFunctionListForControl, setDataOfFunctionListForControl] =
    useState<FunctionList[] | null>(null);

  useEffect(() => {
    if (!functionListForControl || !functionListForCalibration) return;
    const tb1 = functionListForControl.find(
      (item: {
        label: string;
        identifier: string;
        children: { label: string; children: FunctionList[] }[];
      }) => item.identifier === "tb1"
    )?.children;
    if (!tb1 || tb1.length === 0) return;
    const envPumps = tb1.find(
      (item: { label: string; children: FunctionList[] }) =>
        item.label === "Bơm môi trường"
    )?.children;
    if (!envPumps || envPumps.length === 0) return;
    setDataOfFunctionListForControl(envPumps);
  }, [functionListForControl]);

  const [
    dataOfFunctionListForOldCalibration,
    setDataOfFunctionListForOldCalibration,
  ] = useState<FunctionList[] | null>(null);
  useEffect(() => {
    if (!functionListForControl || !functionListForCalibration) return;
    const config = functionListForControl.find(
      (item: {
        label: string;
        identifier: string;
        children: { label: string; children: FunctionList[] }[];
      }) => item.identifier === "config"
    )?.children;
    if (!config || config.length === 0) return;
    const oldCalibrations = config.find(
      (item: { label: string }) => item.label === "Calib bơm môi trường"
    )?.children;
    if (!oldCalibrations || oldCalibrations.length === 0) return;
    setDataOfFunctionListForOldCalibration(oldCalibrations);
  }, []);

  if (
    !dataOfFunctionListForControl ||
    dataOfFunctionListForControl.length === 0 ||
    !functionListForCalibration ||
    functionListForCalibration.length === 0
  )
    return <p>Đang tải dữ liệu</p>;
  else
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: 16,
        }}
      >
        <p style={{ fontSize: 24, fontWeight: "bold" }}>
          Hiệu chuẩn bơm định lượng
        </p>

        {/* Debug Control Panel - Only when debug is available */}
        {/* {shouldShowDebug() && <DebugControlPanel />} */}

        {/* Collapsible guide and test tool */}
        <Collapse
          style={{ marginBottom: 16 }}
          items={[
            {
              key: "guide",
              label: "📖 Hướng dẫn sử dụng",
              children: <CalibrationGuide />,
            },
            // Test tool only when debug enabled
            ...(shouldShowDebugFeature("calibrationTestTool")
              ? [
                  {
                    key: "test",
                    label: "🧪 Test Tool",
                    children: <CalibrationTest />,
                  },
                ]
              : []),
          ]}
        />

        <p style={{ fontSize: 18, fontWeight: "bold", marginBottom: 16 }}>
          Danh sách bơm hiệu chuẩn
        </p>

        <div
          style={{
            display: "flex",
            flexDirection: "row",
            flexWrap: "wrap",
            gap: 16,
          }}
        >
          {functionListForCalibration?.[0]?.children?.[0]?.children.map(
            (item: any, index: number) => {
              return (
                <CalibPump
                  key={index}
                  indexOfPump={index}
                  functionItem={item}
                  functionsForControl={{
                    onOffCoil: dataOfFunctionListForControl[index * 2],
                    digitCoil: dataOfFunctionListForControl[index * 2 + 1],
                  }}
                  functionForOldCalibration={
                    dataOfFunctionListForOldCalibration[index + 1]
                  }
                  isThereAnyCalibration={isThereAnyCalibration}
                  setIsThereAnyCalibration={setIsThereAnyCalibration}
                />
              );
            }
          )}
        </div>
      </div>
    );
};

export default CalibContainer;
