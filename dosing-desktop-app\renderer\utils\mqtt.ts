// export const genAlarmPendingTopic = (siteId: string) => {
//   return `plink/pecom/${siteId}/telementry/upload`;

import { getCustomerIdFromToken, getUserIdFromToken } from "./localStorage";

// };
export const genNoticeSubTopicUserId = () => {
  const userId = getUserIdFromToken();
  return `viis/web-notification/${userId}`;
};
export const genNoticeSubTopicCustomerId = () => {
  const customerID = getCustomerIdFromToken();
  return `viis/web-notification/${customerID}`;
};
export const genNoticeUpdateTopic = () => {
  const userId = getUserIdFromToken();
  return `viis/web-notification/${userId}`;
};

export const genNoticeReadAllTopic = () => {
  const userId = getUserIdFromToken();
  return `viis/web-notification/${userId}/read-all`;
};
export const alarmUpdateTopicMatch = (topic: string) =>
  topic.match(/plink\/pecom\/(.*)\/alarm\/update/);

export const alarmPendingTopicMatch = (topic: string) =>
  topic.match(/plink\/pecom\/(.*)\/telementry\/upload/);

export const genSiteConnectionTopic = (siteId: string) =>
  `plink/pecom/${siteId}/connection`;
export const siteConnectionTopicMatch = (topic: string) =>
  topic.match(/plink\/pecom\/(.*)\/connection/);

export const getSiteInverterConnect = (siteId: string) =>
  `plink/pecom/${siteId}/inverter-connection-list`;

export const siteInvConnTopicMatch = (topic: string) =>
  topic.match(/plink\/pecom\/(.*)\/inverter-connection-list/);
export interface DataInvConnectionRealtime {
  connection_status: number;
  device_sn: string;
  last_connection: string;
  location: string;
  site_name: string;
}
