"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-pagination";
exports.ids = ["vendor-chunks/rc-pagination"];
exports.modules = {

/***/ "../node_modules/rc-pagination/es/locale/en_US.js":
/*!********************************************************!*\
  !*** ../node_modules/rc-pagination/es/locale/en_US.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar locale = {\n  // Options\n  items_per_page: '/ page',\n  jump_to: 'Go to',\n  jump_to_confirm: 'confirm',\n  page: 'Page',\n  // Pagination\n  prev_page: 'Previous Page',\n  next_page: 'Next Page',\n  prev_5: 'Previous 5 Pages',\n  next_5: 'Next 5 Pages',\n  prev_3: 'Previous 3 Pages',\n  next_3: 'Next 3 Pages',\n  page_size: 'Page Size'\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (locale);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL3JjLXBhZ2luYXRpb24vZXMvbG9jYWxlL2VuX1VTLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxNQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uL25vZGVfbW9kdWxlcy9yYy1wYWdpbmF0aW9uL2VzL2xvY2FsZS9lbl9VUy5qcz83YTIyIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBsb2NhbGUgPSB7XG4gIC8vIE9wdGlvbnNcbiAgaXRlbXNfcGVyX3BhZ2U6ICcvIHBhZ2UnLFxuICBqdW1wX3RvOiAnR28gdG8nLFxuICBqdW1wX3RvX2NvbmZpcm06ICdjb25maXJtJyxcbiAgcGFnZTogJ1BhZ2UnLFxuICAvLyBQYWdpbmF0aW9uXG4gIHByZXZfcGFnZTogJ1ByZXZpb3VzIFBhZ2UnLFxuICBuZXh0X3BhZ2U6ICdOZXh0IFBhZ2UnLFxuICBwcmV2XzU6ICdQcmV2aW91cyA1IFBhZ2VzJyxcbiAgbmV4dF81OiAnTmV4dCA1IFBhZ2VzJyxcbiAgcHJldl8zOiAnUHJldmlvdXMgMyBQYWdlcycsXG4gIG5leHRfMzogJ05leHQgMyBQYWdlcycsXG4gIHBhZ2Vfc2l6ZTogJ1BhZ2UgU2l6ZSdcbn07XG5leHBvcnQgZGVmYXVsdCBsb2NhbGU7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../node_modules/rc-pagination/es/locale/en_US.js\n");

/***/ })

};
;