# Cập nhật luồng Calibration theo Node-RED Backend

## Tổng quan thay đổi

Đã cập nhật giao diện app để xử lý đúng theo luồng Node-RED backend hiện tại, thay vì logic cũ.

## Luồng mới (theo Node-RED)

### 1. Quy trình hiệu chuẩn

#### Bước 1: Bơm đầy ống
- User set giá trị lớn vào `HOLDING_SETML_BOM_n` 
- Bật `COIL_BOM_n = true`
- Quan sát và tắt khi đầy ống

#### Bước 2: Chạy hiệu chuẩn
- Set giá trị cần hiệu chuẩn vào `HOLDING_SETML_BOM_n`
- Bật `COIL_BOM_n = true` 
- <PERSON><PERSON> chạy hết thời gian
- <PERSON><PERSON> lượng thực tế ra được

#### Bước 3: <PERSON><PERSON><PERSON><PERSON> kết quả
- <PERSON><PERSON><PERSON><PERSON> gi<PERSON> trị thực tế vào `CALIB_ACTUAL_ML_BOM_n`
- <PERSON><PERSON> gửi qua RPC: `set_state`

#### Bước 4: Trigger tính toán
- App gửi `CALCULATE_CALIB_BOM_n = true`
- Node-RED tự động:
  - Tính toán hệ số mới
  - Ghi xuống `HOLDING_CALIB_BOM_n` qua Modbus
  - Reset `CALCULATE_CALIB_BOM_n = false`

### 2. Công thức tính toán (Node-RED)

```javascript
// Thời gian chạy
RUN_TIME = HOLDING_SETML_BOM_n / HOLDING_CALIB_BOM_n (cũ)

// Hệ số mới  
HOLDING_CALIB_BOM_n (mới) = CALIB_ACTUAL_ML_BOM_n / RUN_TIME
```

## Thay đổi trong code

### 1. CalibPump.tsx

#### Thêm state mới:
```typescript
const [actualMlValue, setActualMlValue] = useState<null | number>(null);
const [isCalibrationInProgress, setIsCalibrationInProgress] = useState(false);
```

#### API calls mới:
```typescript
// Set actual ML value
const handleSetActualMl = async () => {
  const actualMlKey = `CALIB_ACTUAL_ML_BOM_${indexOfPump + 1}`;
  await control({
    device_id_thingsboard: deviceId,
    method: "set_state",
    params: { [actualMlKey]: actualMlValue },
  });
};

// Trigger calibration calculation
const handleTriggerCalibration = async () => {
  const calculateKey = `CALCULATE_CALIB_BOM_${indexOfPump + 1}`;
  await control({
    device_id_thingsboard: deviceId,
    method: "set_state", 
    params: { [calculateKey]: true },
  });
};
```

#### MQTT monitoring:
```typescript
// Monitor calibration completion
useEffect(() => {
  const calculateKey = `CALCULATE_CALIB_BOM_${indexOfPump + 1}`;
  
  const unsubscribe = handleMessage((topic, message) => {
    const data = JSON.parse(message);
    const calculateFlagData = data.find(item => item.key === calculateKey);
    if (calculateFlagData && calculateFlagData.value === false) {
      // Calibration completed
      setIsCalibrationInProgress(false);
      message.success("Hiệu chuẩn hoàn tất!");
    }
  });
  
  return unsubscribe;
}, [deviceId, isCalibrationInProgress]);
```

### 2. UI Components mới

#### CalibrationGuide.tsx
- Hướng dẫn sử dụng step-by-step
- Giải thích công thức tính toán
- Collapsible trong CalibContainer

#### CalibrationDebugInfo.tsx  
- Hiển thị thông tin debug
- Keys sẽ được gửi
- Giá trị dự kiến
- Công thức tính toán
- Collapsible trong mỗi CalibPump

#### CalibrationAPILogger.tsx
- Log các API calls thực tế
- Timeline hiển thị lịch sử
- API calls dự kiến
- Status tracking

### 3. CalibContainer.tsx
- Thêm CalibrationGuide
- Cập nhật title và layout

## Mapping Keys

### Input Keys (từ App → Node-RED):
```
CALIB_ACTUAL_ML_BOM_1 → CALIB_ACTUAL_ML_BOM_14
CALCULATE_CALIB_BOM_1 → CALCULATE_CALIB_BOM_14
```

### Data Keys (Node-RED đọc):
```
HOLDING_SETML_BOM_1 → HOLDING_SETML_BOM_14  (từ holdingRegisterData)
HOLDING_CALIB_BOM_1 → HOLDING_CALIB_BOM_14 (từ holdingRegisterData)
```

### Output Keys (Node-RED ghi):
```
HOLDING_CALIB_BOM_1 → HOLDING_CALIB_BOM_14 (qua Modbus)
```

## API Endpoints sử dụng

### 1. Lấy dữ liệu:
```
GET /api/v2/thingsboard/device-timeseries-latest/{deviceId}?keys=HOLDING_CALIB_BOM_n
```

### 2. Điều khiển thiết bị:
```
POST /api/v2/thingsboard/rpc/oneway/{deviceId}
Body: {
  "method": "set_state",
  "params": {
    "CALIB_ACTUAL_ML_BOM_n": <actual_value>,
    "CALCULATE_CALIB_BOM_n": true
  },
  "timeout": 10000
}
```

## Luồng hoạt động chi tiết

### Frontend (App):
1. User nhập actual ML → gửi `CALIB_ACTUAL_ML_BOM_n`
2. User nhấn "Tính toán" → gửi `CALCULATE_CALIB_BOM_n = true`
3. Monitor MQTT để biết khi nào `CALCULATE_CALIB_BOM_n = false`

### Backend (Node-RED):
1. Inject node chạy mỗi 2 giây
2. Function node kiểm tra `CALCULATE_CALIB_BOM_n = true`
3. Lấy dữ liệu từ global variables
4. Tính toán hệ số mới
5. Ghi xuống Modbus
6. Reset flag về `false`

## Debug và Monitoring

### 1. Debug logs (Node-RED):
```
[PUMP_CALIB_DEBUG] Starting pump calibration check...
[PUMP_CALIB_DEBUG] 🔧 Processing calibration for pump 1...
[PUMP_CALIB_DEBUG] ✅ Pump 1 calculation: NEW_CALIB = 1.44
[PUMP_CALIB_DEBUG] 🚀 Sending 1 modbus write commands
```

### 2. UI Debug Info:
- Hiển thị keys sẽ gửi
- Giá trị hiện tại
- Công thức tính toán
- Trạng thái process

### 3. API Logger:
- Timeline các API calls
- Status tracking
- Expected vs actual calls

## Lưu ý quan trọng

1. **Timing**: Node-RED chạy mỗi 2 giây, có thể có delay
2. **Error Handling**: Node-RED validate dữ liệu trước khi tính toán
3. **Precision**: Kết quả làm tròn 2 chữ số thập phân
4. **MQTT**: Cần subscribe đúng topic để nhận thông báo completion
5. **Global Variables**: Node-RED cần đúng structure của global data

## Testing

### 1. Test case cơ bản:
- Set `HOLDING_SETML_BOM_1 = 100`
- Actual measurement = 120ml
- Expected new calib = 120 / (100 / old_calib)

### 2. Kiểm tra logs:
- Node-RED debug output
- App console logs  
- MQTT messages
- API call timeline

### 3. Validation:
- Kiểm tra giá trị được ghi xuống Modbus
- Verify flag reset về false
- Test với multiple pumps
