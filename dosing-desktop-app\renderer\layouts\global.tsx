import {
  ControlOutlined,
  HistoryOutlined,
  HomeOutlined,
  NotificationFilled,
  NotificationOutlined,
  NotificationTwoTone,
  RightOutlined,
  ScheduleOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { Button, Layout, Menu, message, Modal, Popover } from "antd";
import Link from "next/link";
import { FC, ReactNode, useEffect, useRef, useState } from "react";
import useUserStore from "../stores/userStore";
import { loginOut } from "../services/auth";
import useLanguageStore from "../stores/languageStore";
import { deviceInProjectList, FunctionList } from "../services/device/devices";
import useDeviceDataStore from "../stores/deviceDataStore";
import { useDeviceOnlineChange } from "../services/device/useDeviceOnlineChange";
import useSchedulePlanStore from "../stores/schedulePlanStore";
import { genDeviceTopic } from "../stores/mqttStore.utils";
import { useMqttStore } from "../stores/mqttStore";
import NotificationPopover from "../elements/vietplants/notification";
import { genNoticeUpdateTopic } from "../utils/mqtt";
import { CalibrationInformationProps } from "../stores/deviceDataStore";

const { Header, Content } = Layout;

interface GlobalLayoutProps {
  children?: ReactNode;
}

const GlobalLayout: FC<GlobalLayoutProps> = ({ children }) => {
  const menuItems = [
    {
      key: "1",
      icon: <HomeOutlined />,
      label: <Link href="/vietplants/home">Giám sát</Link>,
    },
    {
      key: "2",
      icon: <ControlOutlined />,
      label: <Link href="/vietplants/control">Điều khiển</Link>,
    },
    {
      key: "3",
      icon: <ScheduleOutlined />,
      label: <Link href="/vietplants/schedule_plan">Lịch pha</Link>,
    },
    {
      key: "4",
      icon: <SettingOutlined />,
      label: <Link href="/vietplants/setting">Cài đặt</Link>,
    },
    {
      key: "5",
      icon: <HistoryOutlined />,
      label: <Link href="/vietplants/program_execution_history">Lịch sử</Link>,
    },
    {
      key: "6",
      icon: <NotificationOutlined />,
      label: <Link href="/vietplants/calibsensors">Hiệu chuẩn</Link>,
    },
  ];

  const languageData = useLanguageStore((state) => state.languageData);

  const { email } = useUserStore();

  // Get list of all projects that user has access to

  // const [listOfAllProjects, setListOfAllProjects] = useState<any[]>([]);
  // const getMyProject = async () => {
  //   try {
  //     // setLoadingResource(true);
  //     const data = await projectList({
  //       filters: [],
  //       page: 1,
  //       size: 1000,
  //       fields: ["*"],
  //       order_by: "creation",
  //     });
  //     setListOfAllProjects(data);
  //   } catch (error: any) {
  //     // message.error(error?.toString());
  //   } finally {
  //     // setLoadingResource(false);
  //   }
  // };

  // useEffect(() => {
  //   getMyProject();
  // }, []);

  //
  // Fetch data of the device
  //
  const {
    deviceData,
    setDeviceData,
    deviceId,
    setDeviceId,
    setIsOnline,
    functionListForControl,
    setFunctionListForControl,
    setFunctionListForMonitor,
    functionListForCalibration,
    setFunctionListForCalibration,
    calibrationInformation,
    setCalibrationInformation,
  } = useDeviceDataStore();

  useEffect(() => {
    console.log("functionListForCalibration: ", functionListForCalibration);
  }, [functionListForCalibration]);

  useEffect(() => {
    if (!email) return;
    // Lưu data vào zustand store để dev
    if (deviceData && Object.keys(deviceData).length > 0) return;
    const fetchSpecificDevice = async () => {
      try {
        const response = await deviceInProjectList({
          // project_id: "dbb01628aa",
          // project_id: "5e809e50-e883-11ec-b13b-4376e531a14a",
          // fields: ["*"],
          // filters: [],
          // or_filters: [],
          // order_by: "",
          // group_by: "",
        });

        // let data = response[0] || undefined;
        let data =
          response.find(
            (device) =>
              device.device_id_thingsboard ===
              "3691dba0-309e-11f0-98dc-bf024c096c4a" // VIETPLANTS - IOT
            // "b0b3cd50-3c27-11f0-98dc-bf024c096c4a" // Test machine
          ) || undefined;
        if (!data) throw new Error("No device found");

        const lastedDataOnline = !!data.latest_data.find(
          (item: any) => item.key === "online" && item.value === true
        );

        setDeviceData(data);
        setDeviceId(data.device_id_thingsboard);
        setIsOnline(lastedDataOnline);
      } catch (error) {
        console.error("Error fetching device list:", error);
      }
    };

    fetchSpecificDevice();
  }, [email]);

  // Handle device online status with the proper hook
  const deviceOnlineData = useDeviceOnlineChange({
    deviceId: deviceId,
    initOnline:
      deviceData && "latest_data" in deviceData
        ? !!deviceData.latest_data?.find(
            (item: any) => item.key === "online" && item.value === true
          )
        : false,
  });

  // Update online status when it changes from the hook
  useEffect(() => {
    if (deviceId) {
      setIsOnline(deviceOnlineData.isOnline);
    }
  }, [deviceOnlineData.isOnline, deviceId, setIsOnline]);

  //
  // Setup function list for UI layout
  //
  const [functionList, setFunctionList] = useState<any[]>([]);
  useEffect(() => {
    if (!deviceData || Object.keys(deviceData).length === 0) return;
    if ("function_list" in deviceData) {
      setFunctionList(deviceData.function_list);
    }
  }, [deviceData]);

  const [
    isFunctionListForControlHasExisted,
    setIsFunctionListForControlHasExisted,
  ] = useState(false);

  useEffect(() => {
    if (functionList.length === 0) return;
    const readonlyFunctionList = [];
    const calibrationFunctionList = [];
    const newStructurizedFunctionList = functionList.reduce(
      (acc: any[], item: FunctionList) => {
        if (item.data_type === "Tab Break") {
          acc.push({
            label: item.label,
            identifier: item.identifier,
            children: [],
          });
          readonlyFunctionList.push({
            label: item.label,
            children: [],
          });
          calibrationFunctionList.push({
            label: item.label,
            identifier: item.identifier,
            children: [],
          });
        } else if (item.data_type === "Group Break") {
          const lastTab = acc[acc.length - 1];
          const lastReadonlyTab =
            readonlyFunctionList[readonlyFunctionList.length - 1];
          const lastCalibrationTab =
            calibrationFunctionList[calibrationFunctionList.length - 1];

          if (lastTab) {
            lastTab.children.push({
              label: item.label,
              children: [],
            });
          }
          if (lastReadonlyTab) {
            lastReadonlyTab.children.push({
              label: item.label,
              children: [],
            });
          }
          if (lastCalibrationTab) {
            lastCalibrationTab.children.push({
              label: item.label,
              children: [],
            });
          }
        } else {
          const lastTab = acc[acc.length - 1];
          const lastReadonlyTab =
            readonlyFunctionList[readonlyFunctionList.length - 1];
          const lastCalibrationTab =
            calibrationFunctionList[calibrationFunctionList.length - 1];

          if (lastTab && lastTab.children.length === 0) {
            lastTab.children.push({
              label: languageData["common.control.tab.config.config"],
              children: [],
            });
          }
          if (lastReadonlyTab && lastReadonlyTab.children.length === 0) {
            lastReadonlyTab.children.push({
              label: languageData["common.control.tab.config.config"],
              children: [],
            });
          }
          if (lastCalibrationTab && lastCalibrationTab.children.length === 0) {
            lastCalibrationTab.children.push({
              label: languageData["common.control.tab.config.config"],
              children: [],
            });
          }

          const lastGroup = lastTab?.children[lastTab.children.length - 1];
          const lastReadonlyGroup =
            lastReadonlyTab?.children[lastReadonlyTab.children.length - 1];
          const lastCalibrationGroup =
            lastCalibrationTab?.children[
              lastCalibrationTab.children.length - 1
            ];

          if (item.data_permission === "rw") {
            lastGroup?.children?.push(item);
          } else if (item.data_permission === "r") {
            lastReadonlyGroup?.children?.push(item);
          }

          lastCalibrationGroup?.children?.push(item);
        }
        return acc;
      },
      []
    );

    setFunctionListForControl(
      newStructurizedFunctionList.filter(
        (item: any) => item.identifier !== "pump_calibration"
      )
    );
    setFunctionListForMonitor(readonlyFunctionList);
    setFunctionListForCalibration(
      calibrationFunctionList.filter(
        (item: any) => item.identifier === "pump_calibration"
      )
    );
    setIsFunctionListForControlHasExisted(true);
  }, [functionList]);

  useEffect(() => {
    if (!functionListForCalibration || functionListForCalibration.length === 0)
      return;

    // Cần check lại, kiểm tra bơm đã bật trước đó chưa thông qua các identifier trong functionListForControl, sau đó set vào giá trị isPumpActivedBefore
    const tmpCalibrationInformation: CalibrationInformationProps[] = Array.from(
      functionListForCalibration?.[0]?.children?.[0]?.children
    ).map(() => ({
      fulfill: false,
      calibration: false,
      startTimestampCalibration: null,
      totalTimeCalibration: 0,
      isPumpActivedBefore: false,
    }));

    setCalibrationInformation(tmpCalibrationInformation);
  }, [functionListForCalibration]);

  const { setScheduleProgramTriggerImmediately } = useSchedulePlanStore();
  useEffect(() => {
    if (!isFunctionListForControlHasExisted) return;

    const schedulePlanTab = functionListForControl.find(
      (item: { label: string; identifier: string; children: FunctionList[] }) =>
        item.identifier === "environment"
    );
    if (!schedulePlanTab || schedulePlanTab.children.length === 0) return;
    const triggerImmediately = schedulePlanTab.children[0]?.children?.[0];

    if (!triggerImmediately) return;
    setScheduleProgramTriggerImmediately(triggerImmediately);
  }, [isFunctionListForControlHasExisted]);

  const { subscribe, unsubscribe } = useMqttStore();
  const idsTopic = useRef<string[]>([]);

  useEffect(() => {
    if (!deviceId) return;
    console.log("reload subscribe for app topic");
    idsTopic.current = subscribe(
      [genDeviceTopic(deviceId), genNoticeUpdateTopic()],
      (message) => {
        console.log("message from broker for app topic:", message);
      }
    );
    return () => {
      unsubscribe(idsTopic.current);
    };
  }, [deviceId]);

  const handleCloseApp = () => {
    window.ipc.send("close-app", null);
  };

  const handleMinimizeApp = () => {
    window.ipc.send("minimize-app", null);
  };

  const [openModal, setOpenModal] = useState(false);

  console.log("functionListForCalibration: ", functionListForCalibration);
  console.log("calibrationInformation: ", calibrationInformation);

  return (
    <Layout>
      <Header
        style={{
          position: "sticky",
          top: 0,
          zIndex: 101,
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          backgroundColor: "rgba(250,250,250,0.5)",
          borderBottom: "solid #ddd 1px",
          backdropFilter: "blur(5px)",
          padding: 0,
          height: "40px",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            height: "60px",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <img
              src="/images/Vietplants-logo.png"
              alt="Logo"
              style={{ height: 30, marginRight: 16, marginLeft: 16 }}
            />
          </div>
          <Menu
            mode="horizontal"
            theme="light"
            items={menuItems}
            style={{
              height: "30px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderBottom: "none",
              backgroundColor: "rgba(250,250,250,0.1)",
              gap: 0,
              overflowX: "scroll",
              minWidth: "700px",
            }}
          />
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 16,
          }}
        >
          <NotificationPopover />
          <Popover
            trigger={"click"}
            content={
              <div style={{ display: "flex", flexDirection: "row", gap: 16 }}>
                <Button
                  type="primary"
                  style={{ marginRight: 16 }}
                  danger
                  onClick={() => {
                    loginOut();
                    window.location.href = "/user/login";
                    message.success(languageData["common.logout.success"]);
                  }}
                >
                  Logout
                </Button>
                <Button
                  style={{
                    borderColor: "orange",
                  }}
                  onClick={handleMinimizeApp}
                >
                  <p style={{ color: "orange" }}>_</p>
                </Button>
                <Button danger onClick={() => setOpenModal(true)}>
                  X
                </Button>
                <Modal
                  open={openModal}
                  onCancel={() => setOpenModal(false)}
                  onOk={() => handleCloseApp()}
                  title="Bạn muốn tắt ứng dụng ?"
                ></Modal>
              </div>
            }
          >
            <p
              style={{
                color: "black",
                marginRight: "20px",
                maxWidth: "150px",
                overflow: "scroll",
                height: "50px",
                fontSize: 12,
              }}
            >
              {email}
            </p>
          </Popover>
        </div>
      </Header>
      <Content
        style={{
          minHeight: "calc(100vh - 40px)",
          backgroundColor: "#f0f4f7",
        }}
      >
        {children}
      </Content>
    </Layout>
  );
};

export default GlobalLayout;
