import { Card, Timeline, Typography, Tag, Alert } from "antd";
import { FC, useEffect, useState } from "react";

const { Text, Paragraph } = Typography;

interface APICall {
  timestamp: string;
  type: "GET" | "POST";
  endpoint: string;
  params?: any;
  description: string;
  status: "pending" | "success" | "error";
}

interface CalibrationAPILoggerProps {
  pumpIndex: number;
  deviceId: string;
}

const CalibrationAPILogger: FC<CalibrationAPILoggerProps> = ({
  pumpIndex,
  deviceId,
}) => {
  const [apiCalls, setApiCalls] = useState<APICall[]>([]);

  // Function to add API call to log
  const logAPICall = (call: Omit<APICall, "timestamp">) => {
    const newCall: APICall = {
      ...call,
      timestamp: new Date().toLocaleTimeString(),
    };
    setApiCalls(prev => [newCall, ...prev].slice(0, 10)); // Keep only last 10 calls
  };

  // Expected API calls for this pump
  const expectedAPICalls = [
    {
      type: "GET" as const,
      endpoint: `/api/v2/thingsboard/device-timeseries-latest/${deviceId}`,
      description: "Lấy giá trị hiệu chuẩn cũ",
      params: { keys: `HOLDING_CALIB_BOM_${pumpIndex + 1}` },
    },
    {
      type: "POST" as const,
      endpoint: `/api/v2/thingsboard/rpc/oneway/${deviceId}`,
      description: "Set giá trị DigitControl (HOLDING_SETML_BOM_n)",
      params: {
        method: "set_state",
        params: { [`HOLDING_SETML_BOM_${pumpIndex + 1}`]: "user_input_value" },
      },
    },
    {
      type: "POST" as const,
      endpoint: `/api/v2/thingsboard/rpc/oneway/${deviceId}`,
      description: "Bật/tắt bơm (COIL_BOM_n)",
      params: {
        method: "set_state",
        params: { [`COIL_BOM_${pumpIndex + 1}`]: true },
      },
    },
    {
      type: "POST" as const,
      endpoint: `/api/v2/thingsboard/rpc/oneway/${deviceId}`,
      description: "Set giá trị thực tế đo được",
      params: {
        method: "set_state",
        params: { [`CALIB_ACTUAL_ML_BOM_${pumpIndex + 1}`]: "actual_ml_value" },
      },
    },
    {
      type: "POST" as const,
      endpoint: `/api/v2/thingsboard/rpc/oneway/${deviceId}`,
      description: "Trigger tính toán hiệu chuẩn",
      params: {
        method: "set_state",
        params: { [`CALCULATE_CALIB_BOM_${pumpIndex + 1}`]: true },
      },
    },
  ];

  const getStatusColor = (status: APICall["status"]) => {
    switch (status) {
      case "pending":
        return "processing";
      case "success":
        return "success";
      case "error":
        return "error";
      default:
        return "default";
    }
  };

  const getTypeColor = (type: APICall["type"]) => {
    return type === "GET" ? "blue" : "green";
  };

  return (
    <Card
      size="small"
      title={`API Calls Log - Bơm ${pumpIndex + 1}`}
      style={{ marginTop: 16 }}
    >
      <Alert
        message="Theo dõi API Calls"
        description="Log này hiển thị các API calls thực tế được gửi đi trong quá trình hiệu chuẩn"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {apiCalls.length > 0 ? (
        <div>
          <Text strong>Lịch sử API Calls:</Text>
          <Timeline
            style={{ marginTop: 16 }}
            items={apiCalls.map((call, index) => ({
              color: getStatusColor(call.status),
              children: (
                <div>
                  <div style={{ display: "flex", alignItems: "center", gap: 8, marginBottom: 4 }}>
                    <Tag color={getTypeColor(call.type)}>{call.type}</Tag>
                    <Tag color={getStatusColor(call.status)}>{call.status}</Tag>
                    <Text type="secondary">{call.timestamp}</Text>
                  </div>
                  <Paragraph style={{ margin: 0, marginBottom: 4 }}>
                    <Text strong>{call.description}</Text>
                  </Paragraph>
                  <Text code style={{ fontSize: 11 }}>
                    {call.endpoint}
                  </Text>
                  {call.params && (
                    <div style={{ marginTop: 4 }}>
                      <Text code style={{ fontSize: 10 }}>
                        {JSON.stringify(call.params, null, 2)}
                      </Text>
                    </div>
                  )}
                </div>
              ),
            }))}
          />
        </div>
      ) : (
        <Alert
          message="Chưa có API calls nào"
          description="Các API calls sẽ được hiển thị ở đây khi bạn thực hiện các thao tác hiệu chuẩn"
          type="info"
        />
      )}

      <div style={{ marginTop: 16 }}>
        <Text strong>API Calls dự kiến cho bơm này:</Text>
        <Timeline
          style={{ marginTop: 8 }}
          items={expectedAPICalls.map((call, index) => ({
            color: "gray",
            children: (
              <div>
                <div style={{ display: "flex", alignItems: "center", gap: 8, marginBottom: 4 }}>
                  <Tag color={getTypeColor(call.type)}>{call.type}</Tag>
                  <Text>{call.description}</Text>
                </div>
                <Text code style={{ fontSize: 11 }}>
                  {call.endpoint}
                </Text>
                <div style={{ marginTop: 4 }}>
                  <Text code style={{ fontSize: 10 }}>
                    {JSON.stringify(call.params, null, 2)}
                  </Text>
                </div>
              </div>
            ),
          }))}
        />
      </div>
    </Card>
  );
};

export default CalibrationAPILogger;
