import React, {
  useRef,
  useEffect,
  useState,
  type FC,
  type ReactNode,
} from "react";
import uPlot from "uplot";
import "uplot/dist/uPlot.min.css";
import { <PERSON><PERSON>, DatePicker } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { getDataTimeSeries } from "../../services/device/devices";
import { getLatestDataDevices } from "../../services/device/devices";
import { useMqttStore } from "../../stores/mqttStore";
import type { FunctionList } from "../../services/device/devices";
import useDeviceDataStore from "../../stores/deviceDataStore";
import useDeviceEcPhTempStore from "../../stores/deviceEcPhTemp";
import { genDeviceTopic } from "../../stores/mqttStore.utils";

interface LineChartProps {
  children?: ReactNode;
  dataFunction?: Partial<FunctionList>;
  lineColor?: string;
}

function determineOptimalParams(startTs: number, endTs: number) {
  const range = endTs - startTs;
  const maxPoints = 200;

  const idealInterval = range / maxPoints;
  const roundedInterval = Math.ceil(idealInterval / 60000) * 60000;
  const limit = Math.ceil(range / roundedInterval);

  return {
    agg: "AVG",
    interval: roundedInterval,
    limit,
  };
}

const LineChart: FC<LineChartProps> = ({
  dataFunction,
  lineColor = "#45c3a1",
}) => {
  const { setEc, setPh, setTemp } = useDeviceEcPhTempStore();
  const { deviceId } = useDeviceDataStore();
  const chartRef = useRef<HTMLDivElement>(null);
  const [currentValue, setCurrentValue] = useState<string>("0");
  const [chart, setChart] = useState<uPlot | null>(null);
  const [chartData, setChartData] = useState<[number[], number[]]>([[], []]);

  const today = dayjs().startOf("day");
  const yesterday = today.subtract(1, "day");
  const [startDateTime, setStartDateTime] = useState<Dayjs | null>(yesterday);
  const [endDateTime, setEndDateTime] = useState<Dayjs | null>(today);

  const [triggerFetchData, setTriggerFetchData] = useState<false | number>(
    false
  );
  // Fetch history on datetime change
  useEffect(() => {
    // if (triggerFetchData === false) return;
    console.log("startDateTime", startDateTime);
    console.log("endDateTime", endDateTime);
    console.log("dataFunction", dataFunction);
    if (
      !startDateTime ||
      !endDateTime ||
      !deviceId ||
      !dataFunction?.identifier
    )
      return;

    const startTs = startDateTime.valueOf();
    const endTs = endDateTime.valueOf();

    if (endTs <= startTs) return; // thêm check tránh fetch sai

    const { interval, agg, limit } = determineOptimalParams(startTs, endTs);

    async function fetchHistory() {
      const res = await getDataTimeSeries({
        keys: dataFunction.identifier,
        startTs,
        endTs,
        agg,
        device_id_thingsboard: deviceId,
        limit,
        interval,
      });

      const rawData = res?.[dataFunction.identifier] || [];
      const x: number[] = [];
      const y: number[] = [];

      rawData.forEach((d) => {
        x.push(d.ts / 1000);
        y.push(parseFloat(d.value));
      });

      setChartData([x, y]);
    }

    fetchHistory();
  }, [
    triggerFetchData,
    startDateTime?.valueOf(),
    endDateTime?.valueOf(),
    dataFunction?.identifier,
    deviceId,
  ]);

  // uPlot rendering
  const title = `${dataFunction?.label || "Untitled"} (${
    dataFunction?.unit || ""
  })`;

  useEffect(() => {
    if (!chartRef.current) return;

    if (chart) {
      chart.destroy();
    }

    const width = chartRef.current.offsetWidth || 600;

    const opts = {
      width,
      height: 150,
      scales: {
        x: { time: true },
        y: { auto: true },
      },
      series: [
        {},
        {
          label: title,
          stroke: lineColor,
          width: 2,
        },
      ],
      axes: [{ stroke: "#999" }, { stroke: "#999" }],
    };

    const newChart = new uPlot(opts, chartData, chartRef.current);
    setChart(newChart);

    const handleResize = () => {
      newChart.destroy();
      const resized = new uPlot(opts, chartData, chartRef.current!);
      setChart(resized);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      newChart.destroy();
      window.removeEventListener("resize", handleResize);
    };
  }, [chartData, title, lineColor]);

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ display: "flex", gap: 8, marginBottom: 8 }}>
        <DatePicker
          showTime
          placeholder="Thời gian bắt đầu"
          value={startDateTime}
          onChange={(val) => {
            if (val && (!startDateTime || !val.isSame(startDateTime))) {
              setStartDateTime(val);
            }
          }}
          style={{ flex: 1 }}
        />
        <DatePicker
          showTime
          placeholder="Thời gian kết thúc"
          value={endDateTime}
          onChange={(val) => {
            if (val && (!endDateTime || !val.isSame(endDateTime))) {
              setEndDateTime(val);
            }
          }}
          style={{ flex: 1 }}
        />
        <Button onClick={() => setTriggerFetchData(Date.now())}>OK</Button>
      </div>

      {/* <div
        style={{
          marginTop: 16,
          display: "flex",
          justifyContent: "space-between",
          marginBottom: 8,
        }}
      >
        <p style={{ fontWeight: "bold" }}>{title}</p>
        <p style={{ color: lineColor }}>
          {currentValue} {dataFunction?.unit}
        </p>
      </div> */}

      <div ref={chartRef} className="chart"></div>
    </div>
  );
};

export default LineChart;
