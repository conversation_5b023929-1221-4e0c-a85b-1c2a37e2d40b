# Giải thích Timer trong Modal "<PERSON><PERSON> hiệu chuẩn cho Bơm X"

## 🕐 Thời gian được tính từ đâu?

### 1. **Thời điểm bắt đầu (startTimestamp)**

Thời gian bắt đầu đượ<PERSON> set khi user **BẬT bơm** trong modal "Tiến hành hiệu chuẩn":

```typescript
// Trong CalibPump.tsx - dòng 80
startTimestampCalibration: responseForCalibration ? dayjs() : null,
```

**Khi nào được set:**
- User bật switch OnOffControl trong modal "Tiến hành hiệu chuẩn"
- `responseForCalibration = true`
- `startTimestampCalibration` = thời điểm hiện tại (`dayjs()`)

### 2. **Tổng thời gian dự kiến (totalTime)**

Thời gian tổng được tính theo công thức:

```typescript
// Trong CalibPump.tsx - dòng 81-82
totalTimeCalibration: oldCalibValue > 0 ? latestHoldingValue / oldCalibValue : 0,
```

**Công thức:**
```
totalTime = HOLDING_SETML_BOM_n / HOLDING_CALIB_BOM_n (cũ)
```

**Ví dụ:**
- `HOLDING_SETML_BOM_1 = 100ml` (lưu lượng cần bơm)
- `HOLDING_CALIB_BOM_1 = 1.2` (hệ số hiệu chuẩn cũ)
- `totalTime = 100 / 1.2 = 83.33 giây`

### 3. **Thời gian đã trôi qua (elapsedTime)**

Được tính real-time trong ModalPreventAction:

```typescript
// Trong ModalPreventAction.tsx - dòng 31-42
const intervalId = setInterval(() => {
  const now = new Date().getTime();           // Thời điểm hiện tại
  const start = startTimestamp.valueOf();     // Thời điểm bắt đầu
  const elapsed = Math.floor((now - start) / 1000);  // Thời gian đã trôi qua (giây)
  
  if (elapsed >= totalTime) {
    handleCancelCalibration();    // Tự động tắt bơm
    setIsCalibProgressFinished(true);  // Đánh dấu hoàn thành
    clearInterval(intervalId);
  }
  setElapsedTime(elapsed);
}, 1000);
```

## 📊 Luồng hoạt động Timer

### Bước 1: Thiết lập thông số
```
User nhập:
- HOLDING_SETML_BOM_1 = 100ml
- HOLDING_CALIB_BOM_1 = 1.2

Tính toán:
- totalTime = 100 / 1.2 = 83.33 giây ≈ 84 giây
```

### Bước 2: Bắt đầu hiệu chuẩn
```
User bật bơm → responseForCalibration = true
→ startTimestampCalibration = dayjs() (ví dụ: 2024-01-15 10:30:00)
→ Hiển thị ModalPreventAction
```

### Bước 3: Đếm ngược
```
Mỗi giây:
- now = thời điểm hiện tại
- elapsed = (now - startTime) / 1000
- Hiển thị: "elapsed / totalTime giây" (ví dụ: "45 / 84 giây")
```

### Bước 4: Kết thúc
```
Khi elapsed >= totalTime:
- Tự động tắt bơm
- Ẩn ModalPreventAction  
- Hiển thị form nhập "actual ML"
```

## 🎯 Ý nghĩa của Timer

### 1. **Thời gian lý thuyết**
- Dựa trên hệ số hiệu chuẩn cũ
- Ước tính thời gian cần để bơm đúng lượng đã set

### 2. **Thời gian thực tế**
- Thời gian bơm thực sự chạy
- Từ lúc bật đến lúc tắt bơm

### 3. **Mục đích**
- **Tự động tắt bơm** khi đủ thời gian lý thuyết
- **Đảm bảo consistency** trong quá trình hiệu chuẩn
- **Tránh bơm quá lâu** gây lãng phí

## 🔧 Code Implementation

### CalibPump.tsx - Set timer parameters:
```typescript
useEffect(() => {
  if (activeModal === "calibration") {
    const newCalibInfo = [...calibrationInformation];
    newCalibInfo[indexOfPump] = {
      ...newCalibInfo[indexOfPump],
      calibration: responseForCalibration,
      startTimestampCalibration: responseForCalibration ? dayjs() : null,  // ⏰ Thời điểm bắt đầu
      totalTimeCalibration: oldCalibValue > 0 ? latestHoldingValue / oldCalibValue : 0,  // ⏱️ Tổng thời gian
    };
    setCalibrationInformation(newCalibInfo);
  }
}, [responseForCalibration, activeModal]);
```

### ModalPreventAction.tsx - Timer logic:
```typescript
useEffect(() => {
  if (!startTimestamp) return;

  const intervalId = setInterval(() => {
    const now = new Date().getTime();
    const start = startTimestamp.valueOf();
    const elapsed = Math.floor((now - start) / 1000);
    
    if (elapsed >= totalTime) {
      handleCancelCalibration();  // Tự động tắt bơm
      setIsCalibProgressFinished(true);
      clearInterval(intervalId);
    }
    setElapsedTime(elapsed);  // Cập nhật UI
  }, 1000);

  return () => clearInterval(intervalId);
}, [startTimestamp]);
```

## 📱 UI Display

```
┌─────────────────────────────────┐
│     Đang hiệu chuẩn cho Bơm 1   │
│                                 │
│          45 / 84 giây           │
│                                 │
│     [Hủy bỏ hiệu chuẩn]        │
└─────────────────────────────────┘
```

## ⚠️ Lưu ý quan trọng

### 1. **Timer chỉ là ước tính**
- Dựa trên hệ số cũ, có thể không chính xác 100%
- Mục đích chính là tự động tắt bơm

### 2. **Thời gian thực tế quan trọng hơn**
- User cần đo lượng thực tế ra được
- Đó mới là cơ sở để tính hệ số mới

### 3. **Có thể hủy bỏ sớm**
- User có thể tắt bơm trước khi hết thời gian
- Nếu thấy đã đủ lượng cần thiết

## 🧮 Ví dụ cụ thể

```
Thiết lập:
- Lưu lượng cần bơm: 100ml
- Hệ số cũ: 1.2
- Thời gian dự kiến: 100/1.2 = 83.33 ≈ 84 giây

Timeline:
10:30:00 - Bật bơm → Timer bắt đầu
10:30:15 - Hiển thị "15 / 84 giây"
10:30:30 - Hiển thị "30 / 84 giây"
...
10:31:24 - Hiển thị "84 / 84 giây" → Tự động tắt bơm

Kết quả:
- Thời gian chạy: 84 giây
- Lượng thực tế: User đo được (ví dụ: 120ml)
- Hệ số mới = 120 / (100/1.2) = 120/83.33 = 1.44
```

## 🎯 Kết luận

Timer trong modal "Đang hiệu chuẩn" được tính từ:
- **Bắt đầu**: Khi user bật bơm trong modal "Tiến hành hiệu chuẩn"
- **Kết thúc**: Khi đạt thời gian dự kiến (SET_ML / OLD_CALIB)
- **Mục đích**: Tự động tắt bơm và chuyển sang bước nhập kết quả đo
