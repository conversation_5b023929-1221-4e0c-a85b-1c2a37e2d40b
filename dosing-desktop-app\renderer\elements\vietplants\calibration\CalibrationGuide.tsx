import { Card, Steps, Alert, Typography } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";

const { Title, Paragraph, Text } = Typography;

const CalibrationGuide = () => {
  const steps = [
    {
      title: "Bơm đầy ống",
      description: "Thiết lập giá trị lớn và bật bơm để đầy ống dẫn",
      content: (
        <div>
          <Paragraph>
            • Nhập giá trị lớn (ví dụ: 1000ml) vào DigitControl
          </Paragraph>
          <Paragraph>• Bật OnOffControl để chạy bơm</Paragraph>
          <Paragraph>• <PERSON>uan sát và tắt bơm khi thấy ống đã đầy</Paragraph>
        </div>
      ),
    },
    {
      title: "Thiết lập thông số hiệu chuẩn",
      description: "Kiểm tra và điều chỉnh các thông số trước khi hiệu chuẩn",
      content: (
        <div>
          <Paragraph>
            • <Text strong><PERSON><PERSON><PERSON> l<PERSON> bơm:</Text> Nhập gi<PERSON> trị cần hiệu chuẩn (ví
            dụ: 100ml)
          </Paragraph>
          <Paragraph>
            • <Text strong>Hệ số hiệu chuẩn cũ:</Text> Kiểm tra và có thể điều
            chỉnh nếu cần
          </Paragraph>
          <Paragraph>• Bật bơm và quan sát quá trình</Paragraph>
          <Paragraph>
            • <Text strong>Tùy chọn:</Text> Có thể nhập kết quả ngay khi đo xong
            hoặc chờ hết thời gian
          </Paragraph>
          <Paragraph>• Đo lượng thực tế ra được</Paragraph>
        </div>
      ),
    },
    {
      title: "Nhập kết quả đo",
      description: "Nhập lượng thực tế đo được vào hệ thống",
      content: (
        <div>
          <Paragraph>• Nhập chính xác lượng ml thực tế đo được</Paragraph>
          <Paragraph>• Nhấn "Lưu" để gửi giá trị lên hệ thống</Paragraph>
        </div>
      ),
    },
    {
      title: "Tính toán hệ số mới",
      description: "Hệ thống tự động tính toán và cập nhật hệ số hiệu chuẩn",
      content: (
        <div>
          <Paragraph>• Nhấn "Tính toán và cập nhật hệ số hiệu chuẩn"</Paragraph>
          {/* <Paragraph>• Node-RED sẽ tự động tính toán theo công thức:</Paragraph> */}
          {/* <Text code>
            RUN_TIME = SET_ML / CURRENT_CALIB
            <br />
            NEW_CALIB = ACTUAL_ML / RUN_TIME
          </Text>
          <Paragraph style={{ marginTop: 8 }}>
            • Hệ số mới sẽ được ghi xuống thiết bị qua Modbus
          </Paragraph> */}
        </div>
      ),
    },
  ];

  return (
    <Card
      title={
        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
          <InfoCircleOutlined />
          <span>Hướng dẫn hiệu chuẩn bơm</span>
        </div>
      }
      style={{ marginBottom: 16 }}
    >
      <Alert
        message="Lưu ý quan trọng"
        description="Quá trình hiệu chuẩn cần thực hiện theo đúng thứ tự các bước. Đảm bảo đo chính xác lượng thực tế để có kết quả hiệu chuẩn tốt nhất."
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Steps
        direction="vertical"
        size="small"
        items={steps.map((step, index) => ({
          title: step.title,
          description: step.description,
          status: "wait",
        }))}
      />

      <div style={{ marginTop: 16 }}>
        {steps.map((step, index) => (
          <Card
            key={index}
            size="small"
            title={`Bước ${index + 1}: ${step.title}`}
            style={{ marginBottom: 8 }}
          >
            {step.content}
          </Card>
        ))}
      </div>

      {/* <Alert
        message="Công thức tính toán"
        description={
          <div>
            <Paragraph>
              <Text strong>Thời gian chạy:</Text> RUN_TIME = HOLDING_SETML_BOM_n
              / HOLDING_CALIB_BOM_n (cũ)
            </Paragraph>
            <Paragraph>
              <Text strong>Hệ số mới:</Text> HOLDING_CALIB_BOM_n (mới) =
              CALIB_ACTUAL_ML_BOM_n / RUN_TIME
            </Paragraph>
          </div>
        }
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      /> */}
    </Card>
  );
};

export default CalibrationGuide;
