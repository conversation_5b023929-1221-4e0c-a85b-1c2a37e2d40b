import { FC, useEffect, useState } from "react";
import useSchedulePlanStore, {
  ScheduleAction,
  ScheduleProgram,
} from "../../../../stores/schedulePlanStore";
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Switch,
  TimePicker,
} from "antd";
import dayjs from "dayjs";
import useDeviceDataStore from "../../../../stores/deviceDataStore";
import { FunctionList } from "../../../../services/device/devices";
import { generateAPIPath } from "../../../../services/utilities";
import { DashboardOutlined } from "@ant-design/icons";
import { updateScheduleProgram } from "../../../../services/schedule";
import InputTextWithKeyboard from "../../../../components/virtual-input/InputTextWithKeyboard";
import InputNumberWithKeyboard from "../../../../components/virtual-input/InputNumberWithKeyboard";

interface DetailedProgramProps {
  program: ScheduleProgram;
  onClose: () => void;
  start_date_of_plan: string;
  end_date_of_plan: string;
}

const DetailedProgram: FC<DetailedProgramProps> = ({
  program,
  onClose,
  start_date_of_plan,
  end_date_of_plan,
}) => {
  const [form] = Form.useForm();
  const { schedulePlans, setSchedulePlans, scheduleProgramTriggerImmediately } =
    useSchedulePlanStore();

  const [dates, setDates] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs(program.start_date),
    dayjs(program.end_date),
  ]);
  const [startDate, endDate] = dates;
  const [startTime, endTime] = [
    dayjs(program.start_time, "HH:mm:ss"),
    dayjs(program.end_time, "HH:mm:ss"),
  ];

  const [intervalDays, setIntervalDays] = useState([]);
  useEffect(() => {
    if (program?.interval) {
      setIntervalDays(program.interval.split(","));
    }
  }, [program?.interval]);

  const { functionListForControl } = useDeviceDataStore();

  const [options, setOptions] = useState<{ value: string; label: string }[]>(
    []
  );
  useEffect(() => {
    if (!scheduleProgramTriggerImmediately) return;
    setOptions(
      scheduleProgramTriggerImmediately.enum_value.split(",").map((item) => ({
        value: item.trim(),
        label: item.trim(),
      }))
    );
  }, [scheduleProgramTriggerImmediately]);

  const onFinish = async (values: any) => {
    try {
      console.log("values: ", values);

      const action: ScheduleAction = Object.fromEntries(
        Object.entries(values.action || {}).map(([key, value]) => {
          if (typeof value === "boolean") {
            return [key, String(value)];
          } else if (typeof value === "number" || typeof value === "string") {
            return [key, value];
          } else {
            return [key, String(value)];
          }
        })
      );

      const programToPush = {
        id: program.id,
        name: values.name,
        // start_time: values.start_time.format("HH:mm:ss"),
        start_time: "00:00:00",
        // end_time: values.start_time
        //   .add(values.time_running, "seconds")
        //   .format("HH:mm:ss"),
        end_time: "00:00:00",
        start_date: dates[0].format("YYYY-MM-DD"),
        end_date: dates[1].format("YYYY-MM-DD"),
        interval: values.interval.join(","),
        enable: values.enable ? 1 : 0,
        schedule_plan_id: program.schedule_plan_id,
        device_id: program.device_id,
        type: "",
        action: action,
      };
      console.log("programToPush: ", programToPush);
      const res = await updateScheduleProgram(programToPush);
      if (res?.statusOK) {
        message.success("Chỉnh sửa chương trình thành công");
        const updatedPlans = schedulePlans.map((plan) => {
          if (plan.name === program.schedule_plan_id) {
            return {
              ...plan,
              schedules: plan.schedules.map((schedule) => {
                if (schedule.id === program.id) {
                  return res?.responseData?.result?.data;
                }
                return schedule;
              }),
            };
          }
          return plan;
        });
        setSchedulePlans(updatedPlans);
        onClose();
      }
    } catch (error) {
      message.error(
        "Có lỗi xảy ra khi chỉnh sửa chương trình ! Vui lòng thử lại"
      );
    }
  };

  console.log("program right now: ", program);
  console.log("intervalDays: ", intervalDays);

  return (
    <Form layout="vertical" form={form} style={{ width: "100%" }}>
      <div
        style={{
          zIndex: 100,
          position: "fixed",
          bottom: 24,
          right: 24,
          display: "flex",
          justifyContent: "flex-end",
          gap: 8,
          padding: 8,
          background: "rgba(255, 255, 255, 0.5)",
          borderRadius: 8,
          backdropFilter: "blur(5px)",
          border: "1px solid #ddd",
          boxShadow: "0px 0px 50px 2px rgba(0, 0, 0, 0.25)",
        }}
      >
        <Button onClick={() => onClose()}>Hủy</Button>
        <Button type="primary" onClick={() => onFinish(form.getFieldsValue())}>
          Lưu
        </Button>
      </div>

      {/* <Form.Item
        name="interval"
        label="Áp dụng cho các thứ"
        rules={[{ required: true }]}
        initialValue={intervalDays}
      >
        <Checkbox.Group
          options={[
            { label: "Chủ Nhật", value: "0" },
            { label: "Thứ 2", value: "1" },
            { label: "Thứ 3", value: "2" },
            { label: "Thứ 4", value: "3" },
            { label: "Thứ 5", value: "4" },
            { label: "Thứ 6", value: "5" },
            { label: "Thứ 7", value: "6" },
          ]}
          onChange={(e) => setIntervalDays(e)}
        />
      </Form.Item> */}

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Form.Item
            name="name"
            label="Tên chương trình"
            rules={[{ required: true }]}
            layout="vertical"
            initialValue={program.name}
          >
            <InputTextWithKeyboard style={{ width: "100%" }} />
          </Form.Item>
        </Col>
      </Row>

      {/* <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item
            name="start_time"
            label="Thời gian bắt đầu"
            rules={[{ required: true }]}
            layout="vertical"
            initialValue={startTime}
          >
            <TimePicker style={{ width: "100%" }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="time_running"
            initialValue={dayjs(program.end_time, "HH:mm:ss").diff(
              dayjs(program.start_time, "HH:mm:ss"),
              "second"
            )}
            label="Thời gian thực hiện (Giây)"
            rules={[{ required: true }]}
            layout="vertical"
          >
            <InputNumberWithKeyboard style={{ width: "100%" }} />
          </Form.Item>
        </Col>
      </Row> */}

      {/* <Row gutter={[16, 16]}>
        <Col span={24}>
          <Form.Item
            rules={[{ required: true }]}
            label="Ngày thực hiện"
            initialValue={[startDate, endDate]}
          >
            <DatePicker.RangePicker
              style={{ width: "100%" }}
              key="date_range_picker"
              onChange={(values) =>
                setDates(values as [dayjs.Dayjs, dayjs.Dayjs])
              }
              defaultValue={[startDate, endDate]}
              // format={"DD/MM/YYYY"}
              disabledDate={(current) => {
                // Disable dates that are not within the selectedPlan's start and end dates
                const today = dayjs().startOf("day");
                const startDate = dayjs(start_date_of_plan);
                const exactStartDate = today.isBefore(startDate)
                  ? startDate
                  : today;
                const endDate = dayjs(end_date_of_plan);

                return (
                  current && (current < exactStartDate || current > endDate)
                );
              }}
            />
          </Form.Item>
        </Col>
      </Row> */}

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item
            name={["action", "env_enum"]}
            rules={[{ required: true }]}
            label="Mã môi trường"
            initialValue={program?.action?.env_enum}
          >
            <Select
              placeholder="Chọn mã môi trường"
              style={{ width: "100%" }}
              options={options}
            />
          </Form.Item>
        </Col>
      </Row>

      <Col span={24} style={{ marginTop: 32 }}>
        {functionListForControl
          .find((fn) => fn.identifier === "tb1")
          ?.children?.map((functionItem) =>
            functionItem.children.length === 0 ? null : (
              <Row key={functionItem.label} style={{ marginBottom: 32 }}>
                <p style={{ margin: 0, fontSize: 16, fontWeight: "bold" }}>
                  {functionItem.label}
                </p>
                <Col span={24} style={{ marginTop: 8 }}>
                  {functionItem?.children?.map(
                    (functionItemChild: FunctionList) => (
                      <Row
                        gutter={[16, 16]}
                        style={{ borderTop: "1px solid #ddd" }}
                        key={functionItemChild.identifier}
                      >
                        <Col span={24}>
                          <Form.Item
                            style={{ marginBottom: 0 }}
                            name={["action", functionItemChild.identifier]}
                            initialValue={
                              program.action[functionItemChild.identifier]
                            }
                            layout="horizontal"
                            labelCol={{
                              span: 12,
                              style: { textAlign: "left" },
                            }}
                            wrapperCol={{
                              span: 12,
                              style: { textAlign: "right" },
                            }}
                            colon={false}
                            label={
                              <div
                                style={{
                                  display: "flex",
                                  flexDirection: "row",
                                  alignItems: "center",
                                  content: "",
                                }}
                              >
                                {functionItemChild.icon_url ? (
                                  <img
                                    height={"24px"}
                                    src={generateAPIPath(
                                      "api/v2/file/download?file_url=" +
                                        functionItemChild.icon_url
                                    )}
                                    onError={() => <DashboardOutlined />}
                                  />
                                ) : (
                                  <DashboardOutlined />
                                )}
                                <p style={{ margin: 0, marginLeft: 8 }}>
                                  {functionItemChild.label}
                                </p>
                              </div>
                            }
                          >
                            {functionItemChild.data_type === "Bool" && (
                              <Switch
                                style={{ width: 40 }}
                                checked={
                                  form.getFieldValue([
                                    "action",
                                    functionItemChild.identifier,
                                  ]) === "true"
                                }
                                onChange={(checked) => {
                                  form.setFieldValue(
                                    ["action", functionItemChild.identifier],
                                    checked.toString()
                                  );
                                }}
                              />
                            )}
                            {functionItemChild.data_type === "Value" && (
                              // <InputNumber
                              //   defaultValue={
                              //     typeof program.action[
                              //       functionItemChild.identifier
                              //     ] === "number"
                              //       ? (program.action[
                              //           functionItemChild.identifier
                              //         ] as number)
                              //       : 0
                              //   }
                              //   style={{
                              //     width: 200,
                              //     marginTop: 4,
                              //     marginBottom: 4,
                              //   }}
                              // />
                              <InputNumberWithKeyboard
                                defaultValue={
                                  typeof program.action[
                                    functionItemChild.identifier
                                  ] === "number"
                                    ? (program.action[
                                        functionItemChild.identifier
                                      ] as number)
                                    : 0
                                }
                                style={{
                                  width: 200,
                                  marginTop: 4,
                                  marginBottom: 4,
                                }}
                              />
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                    )
                  )}
                </Col>
              </Row>
            )
          )}
      </Col>
    </Form>
  );
};

export default DetailedProgram;
