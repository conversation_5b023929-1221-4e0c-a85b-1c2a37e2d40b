import React, { useState, useRef, useEffect } from "react";
import { InputNumber } from "antd";
import Keyboard from "react-simple-keyboard";
import "react-simple-keyboard/build/css/index.css";
import { InputNumberProps } from "antd";

const InputNumberWithKeyboard = React.forwardRef(
  (props: InputNumberProps, ref: any) => {
    const [inputValue, setInputValue] = useState<string | undefined>("");
    const [keyboardVisible, setKeyboardVisible] = useState(false);
    const keyboardInstanceRef = useRef(null);
    const inputRef = useRef<any>(null);
    const keyboardWrapperRef = useRef<any>(null);

    useEffect(() => {
      if (props.defaultValue !== undefined && props.defaultValue !== null) {
        setInputValue(String(props.defaultValue));
      }
    }, [props.defaultValue]);

    useEffect(() => {
      if (ref) {
        ref.current = inputRef.current;
      }
    }, [ref]);

    useEffect(() => {
      if (
        props.value !== undefined &&
        props.value !== null &&
        String(props.value) !== inputValue
      ) {
        setInputValue(String(props.value));
      }
    }, [props.value]);

    const handleKeyboardChange = (input: string) => {
      // const cleaned = input.replace(/[^0-9.]/g, "");
      console.log("input: ", input);
      const cleaned = input;
      const numberValue = Number(cleaned);
      const isValid = cleaned !== "" && !isNaN(numberValue);
      if (!cleaned || !isValid) {
        setInputValue(undefined);
        if (props.onChange) {
          // props.onChange({ target: { value: undefined, name: props.name } });
          props.onChange?.(undefined);
        }
      } else {
        setInputValue(cleaned);
        if (props.onChange) {
          // props.onChange({ target: { value: numberValue, name: props.name } });
          props.onChange(numberValue);
        }
      }
    };

    const handleFocus = () => {
      setKeyboardVisible(true);
      setTimeout(() => {
        keyboardInstanceRef.current?.setInput(inputValue);
      }, 100);
    };

    const handleKeyPress = (button: string) => {
      if (button === "{bksp}") {
        handleKeyboardChange(inputValue?.slice(0, -1) || "");
        return;
      }
      if (button === "{enter}") {
        setKeyboardVisible(false);
        inputRef.current?.blur();
        return;
      }
      // Prevent adding multiple decimal points
      if (button === "." && (inputValue || "").includes(".")) {
        return;
      }
      handleKeyboardChange((inputValue || "") + button);
    };

    const handleInputChange = (value: any) => {
      const numberValue = Number(value);
      const isValid = value !== "" && !isNaN(numberValue);

      if (!isValid) return;

      // Preserve decimal format in string representation
      const stringValue =
        typeof value === "string" && value.includes(".")
          ? numberValue.toString()
          : numberValue.toString();

      setInputValue(stringValue);

      if (props.onChange) {
        // props.onChange({ target: { value: numberValue, name: props.name } });
        props.onChange(numberValue);
      }
    };

    const handleBlur = () => {
      const val = inputValue;
      const cleaned = val?.replace(/[^0-9.]/g, "");
      const numberValue = Number(cleaned);
      if (!cleaned || isNaN(numberValue)) {
        setInputValue(undefined);
        // props.onChange?.({ target: { value: undefined, name: props.name } });
        props.onChange?.(undefined);
      } else {
        setInputValue(cleaned);
        // props.onChange?.({ target: { value: numberValue, name: props.name } });
        props.onChange?.(numberValue);
      }
    };

    useEffect(() => {
      const handleClickOutside = (event: any) => {
        if (
          inputRef.current &&
          !inputRef.current.inputElement?.contains(event.target) &&
          !keyboardWrapperRef.current?.contains(event.target)
        ) {
          setKeyboardVisible(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);

    return (
      <>
        <InputNumber
          {...props}
          ref={inputRef}
          value={
            inputValue === "" || isNaN(Number(inputValue))
              ? undefined
              : Number(inputValue)
          }
          defaultValue={props.defaultValue ?? 0}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onClick={handleFocus}
          // Explicitly allow decimal values
          precision={props.precision !== undefined ? props.precision : 2}
          step={props.step !== undefined ? props.step : 0.01}
        />
        {keyboardVisible && (
          <div
            ref={keyboardWrapperRef}
            className="rsk-container"
            style={{
              position: "fixed",
              bottom: 0,
              left: 0,
              width: "100%",
              background: "rgba(255,255,255,0.2)",
              boxShadow: "0 -2px 8px rgba(0,0,0,0.15)",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                padding: "8px 16px",
                borderBottom: "1px solid #e8e8e8",
                fontSize: "16px",
                fontWeight: "bold",
                background: "#fafafa",
                whiteSpace: "nowrap",
                overflowX: "auto",
              }}
            >
              {inputValue || "\u00A0"}
            </div>
            <Keyboard
              ref={keyboardInstanceRef}
              onChange={handleKeyboardChange}
              onKeyPress={handleKeyPress}
              layout={{
                default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}", "{enter}"],
              }}
              display={{
                "{bksp}": "⌫",
                "{enter}": "⏎",
              }}
            />
          </div>
        )}
      </>
    );
  }
);

export default InputNumberWithKeyboard;
