import { FC, useEffect, useState } from "react";
import {
  FunctionList,
  getLatestDataDevices,
} from "../../../services/device/devices";
import InputNumberWithKeyboard from "../../../components/virtual-input/InputNumberWithKeyboard";
import useDeviceDataStore from "../../../stores/deviceDataStore";
import { Button, Modal, Collapse, message } from "antd";
import DigitControl from "../../../components/control/DigitControl";
import OnOffControl from "../../../components/control/OnOffControl";
import ModalPreventAction from "./ModalPreventAction";
import useControlDevice from "../../../services/device/useControlDevice";
import useLatestDataDevice from "../../../services/device/useLatestDataDevice";
import dayjs, { Dayjs } from "dayjs";
import { useMqttStore } from "../../../stores/mqttStore";
import { genDeviceTopic } from "../../../stores/mqttStore.utils";
import CalibrationDebugInfo from "./CalibrationDebugInfo";
import { shouldShowDebugFeature } from "../../../utils/debugMode";

type CalibPumpProps = {
  key: number;
  indexOfPump: number;
  functionItem: FunctionList;
  functionsForControl: {
    onOffCoil: FunctionList;
    digitCoil: FunctionList;
  };
  functionForOldCalibration: FunctionList;
  isThereAnyCalibration: null | FunctionList;
  setIsThereAnyCalibration: (value: null | FunctionList) => void;
};

const CalibPump: FC<CalibPumpProps> = ({
  key,
  indexOfPump,
  functionItem,
  functionsForControl,
  functionForOldCalibration,
  isThereAnyCalibration,
  setIsThereAnyCalibration,
}) => {
  console.log("key ne: ", indexOfPump);
  const { calibrationInformation, setCalibrationInformation, deviceId } =
    useDeviceDataStore();

  // const [isTankBeingPumped, setIsTankBeingPumped] = useState(false);
  const [responseForFulfill, setResponseForFulfill] = useState(false);
  const [openModal1, setOpenModal1] = useState(false);
  // Track which modal is currently open to handle state correctly
  const [activeModal, setActiveModal] = useState<
    "fulfill" | "calibration" | null
  >(null);

  useEffect(() => {
    if (!calibrationInformation || calibrationInformation.length === 0) return;

    // Only update fulfill state if the fulfill modal is active
    if (activeModal === "fulfill") {
      const newCalibInfo = [...calibrationInformation];
      newCalibInfo[indexOfPump] = {
        ...newCalibInfo[indexOfPump],
        fulfill: responseForFulfill,
      };
      setCalibrationInformation(newCalibInfo);
    }
  }, [responseForFulfill, activeModal]);

  // const [isPumpBeingCalibrated, setIsPumpBeingCalibrated] = useState(false);
  const [responseForCalibration, setResponseForCalibration] = useState(false);
  const [openModal2, setOpenModal2] = useState(false);

  useEffect(() => {
    if (!calibrationInformation || calibrationInformation.length === 0) return;

    // Only update calibration state if the calibration modal is active
    if (activeModal === "calibration") {
      const newCalibInfo = [...calibrationInformation];
      newCalibInfo[indexOfPump] = {
        ...newCalibInfo[indexOfPump],
        calibration: responseForCalibration,
        startTimestampCalibration: responseForCalibration ? dayjs() : null,
        totalTimeCalibration:
          oldCalibValue > 0 ? latestHoldingValue / oldCalibValue : 0,
      };
      setCalibrationInformation(newCalibInfo);
    }
  }, [responseForCalibration, activeModal]);

  const { run: control } = useControlDevice();
  const handleCancelCalibration = async () => {
    const newCalibInfo = [...calibrationInformation];
    newCalibInfo[indexOfPump] = {
      ...newCalibInfo[indexOfPump],
      calibration: false,
    };

    await control({
      device_id_thingsboard: deviceId,
      method: "set_state",
      params: {
        [functionsForControl.onOffCoil.identifier]: false,
      },
    });

    setCalibrationInformation(newCalibInfo);
    setIsThereAnyCalibration(null);
    setActiveModal(null);
  };

  const [oldCalibValue, setOldCalibValue] = useState<null | number>(null);

  // State for actual ML value input by user
  const [actualMlValue, setActualMlValue] = useState<null | number>(null);

  // State for calibration process
  const [isCalibrationInProgress, setIsCalibrationInProgress] = useState(false);

  // State to force refresh data in modal
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle setting actual ML value
  const handleSetActualMl = async () => {
    if (!actualMlValue) return;

    const actualMlKey = `CALIB_ACTUAL_ML_BOM_${indexOfPump + 1}`;

    console.log({
      device_id_thingsboard: deviceId,
      method: "set_state",
      params: {
        [actualMlKey]: actualMlValue,
      },
    });

    await control({
      device_id_thingsboard: deviceId,
      method: "set_state",
      params: {
        [actualMlKey]: actualMlValue,
      },
    });
  };

  // Handle triggering calibration calculation
  const handleTriggerCalibration = async () => {
    const calculateKey = `CALCULATE_CALIB_BOM_${indexOfPump + 1}`;

    console.log({
      device_id_thingsboard: deviceId,
      method: "set_state",
      params: {
        [calculateKey]: true,
      },
    });

    try {
      await control({
        device_id_thingsboard: deviceId,
        method: "set_state",
        params: {
          [calculateKey]: true,
        },
      });

      setIsCalibrationInProgress(true);
      message.success("Đã gửi yêu cầu tính toán hiệu chuẩn");

      // Set timeout to reset progress state if no response after 30 seconds
      setTimeout(() => {
        setIsCalibrationInProgress(false);
      }, 30000);
    } catch (error) {
      message.error("Lỗi khi gửi yêu cầu hiệu chuẩn");
      console.error("Calibration trigger error:", error);
    }
  };

  // MQTT subscription to monitor calibration flag reset
  const { subscribe, unsubscribe } = useMqttStore();

  useEffect(() => {
    if (!deviceId || !isCalibrationInProgress) return;

    const calculateKey = `CALCULATE_CALIB_BOM_${indexOfPump + 1}`;

    // Subscribe to device topic to monitor flag changes
    const subscriptionIds = subscribe([genDeviceTopic(deviceId)], (msg) => {
      try {
        const data = JSON.parse(msg);
        if (Array.isArray(data)) {
          const calculateFlagData = data.find(
            (item) => item.key === calculateKey
          );
          if (calculateFlagData && calculateFlagData.value === false) {
            // Calibration completed (flag reset to false by Node-RED)
            setIsCalibrationInProgress(false);
            message.success("Hiệu chuẩn hoàn tất! Hệ số mới đã được cập nhật.");
          }
        }
      } catch (error) {
        console.error("MQTT message parsing error:", error);
      }
    });

    return () => {
      if (subscriptionIds.length > 0) {
        unsubscribe(subscriptionIds);
      }
    };
  }, [deviceId, isCalibrationInProgress, indexOfPump, subscribe, unsubscribe]);

  // Get initial calibration coefficient value and subscribe to MQTT updates
  useEffect(() => {
    if (!functionForOldCalibration?.identifier || !deviceId) return;

    // Get latest data for calibration coefficient
    getLatestDataDevices({
      deviceId: deviceId,
      keys: [functionForOldCalibration.identifier],
    }).then((res) => {
      if (!res) return;
      const latestCalibData =
        res?.data?.[functionForOldCalibration.identifier] || [];
      console.log("latestDataOfOldCalibration", latestCalibData);
      if (latestCalibData.length > 0) {
        const latestData = latestCalibData[latestCalibData.length - 1];
        if (latestData) {
          const calibValue = Number(latestData?.value);
          setCurrentCalibCoeff(calibValue);
          setOldCalibValue(calibValue);
        }
      }
    });

    // Subscribe to MQTT for real-time updates
    const subscriptionIds = subscribe([genDeviceTopic(deviceId)], (msg) => {
      try {
        const data = JSON.parse(msg);
        if (Array.isArray(data)) {
          const calibData = data.find(
            (item) => item.key === functionForOldCalibration.identifier
          );
          if (calibData) {
            const calibValue = Number(calibData.value);
            setCurrentCalibCoeff(calibValue);
            setOldCalibValue(calibValue);
          }
        }
      } catch (error) {
        console.error("MQTT message parsing error for calib coeff:", error);
      }
    });

    return () => {
      if (subscriptionIds.length > 0) {
        unsubscribe(subscriptionIds);
      }
    };
  }, [deviceId, functionForOldCalibration?.identifier, subscribe, unsubscribe]);

  // Force refresh data when modal opens
  useEffect(() => {
    if (refreshTrigger === 0) return; // Skip initial render

    // Refresh calibration coefficient data
    if (functionForOldCalibration?.identifier && deviceId) {
      getLatestDataDevices({
        deviceId: deviceId,
        keys: [functionForOldCalibration.identifier],
      }).then((res) => {
        if (!res) return;
        const latestCalibData =
          res?.data?.[functionForOldCalibration.identifier] || [];
        if (latestCalibData.length > 0) {
          const latestData = latestCalibData[latestCalibData.length - 1];
          if (latestData) {
            const calibValue = Number(latestData?.value);
            setCurrentCalibCoeff(calibValue);
            setOldCalibValue(calibValue);
          }
        }
      });
    }

    // Refresh holding value data
    if (functionsForControl?.digitCoil?.identifier && deviceId) {
      getLatestDataDevices({
        deviceId: deviceId,
        keys: [functionsForControl.digitCoil.identifier],
      }).then((res) => {
        if (!res) return;
        const latestHoldingData =
          res?.data?.[functionsForControl.digitCoil.identifier] || [];
        if (latestHoldingData.length > 0) {
          const latestData = latestHoldingData[latestHoldingData.length - 1];
          if (latestData) {
            setLatestHoldingValue(Number(latestData?.value));
          }
        }
      });
    }
  }, [
    refreshTrigger,
    deviceId,
    functionForOldCalibration?.identifier,
    functionsForControl?.digitCoil?.identifier,
  ]);

  console.log("functionForHolding: ", functionsForControl?.digitCoil);
  console.log("functionForOldCalibration: ", functionForOldCalibration);

  const [latestHoldingValue, setLatestHoldingValue] = useState<null | number>(
    null
  );

  // State for current calibration coefficient (HOLDING_CALIB_BOM_n)
  const [currentCalibCoeff, setCurrentCalibCoeff] = useState<null | number>(
    null
  );

  const [isCalibProgressFinished, setIsCalibProgressFinished] =
    useState<boolean>(false);

  if (!calibrationInformation || calibrationInformation.length === 0)
    return <p>Đang tải dữ liệu !</p>;
  else
    return (
      <div
        key={key}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 8,
          borderRadius: 8,
          padding: 8,
          width: "calc(50% - 16px)",
          backgroundColor: "#fff",
          height: "100%",
        }}
      >
        <p
          style={{
            marginBottom: 16,
            fontWeight: "bold",
            color: "rgb(40,40,40)",
          }}
        >
          {functionItem.label}
        </p>
        <div
          style={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 8,
          }}
        >
          <div
            style={{
              width: "100%",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              height: "100%",
              gap: 8,
            }}
          >
            <Button
              style={{ width: "50%" }}
              onClick={() => {
                setOpenModal1(true);
                setActiveModal("fulfill");
              }}
            >
              <p
                style={{
                  fontSize: 12,
                  margin: 0,
                  color: calibrationInformation?.[indexOfPump]?.fulfill
                    ? "rgb(153, 255, 0)"
                    : "rgb(100,100,100)",
                  fontWeight: "bold",
                }}
              >
                Bơm đầy ống
              </p>
            </Button>
            <Button
              style={{ width: "50%" }}
              onClick={() => {
                setOpenModal2(true);
                setActiveModal("calibration");
              }}
            >
              <p
                style={{
                  fontSize: 12,
                  margin: 0,
                  color: calibrationInformation?.[indexOfPump]?.calibration
                    ? "rgb(153, 255, 0)"
                    : "rgb(100,100,100)",
                  fontWeight: "bold",
                }}
              >
                Tiến hành hiệu chuẩn
              </p>
            </Button>
          </div>

          {/* Display current calibration coefficient */}
          {functionForOldCalibration && (
            <div
              style={{
                width: "100%",
                display: "flex",
                flexDirection: "column",
                gap: 4,
                marginTop: 8,
                padding: 8,
                backgroundColor: "#f8f9fa",
                borderRadius: 4,
                border: "1px solid #e9ecef",
              }}
            >
              <p
                style={{
                  margin: 0,
                  fontSize: 12,
                  fontWeight: "bold",
                  color: "#495057",
                }}
              >
                {functionForOldCalibration.label}:
              </p>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 8,
                }}
              >
                <InputNumberWithKeyboard
                  value={currentCalibCoeff}
                  readOnly
                  disabled
                  style={{
                    width: "100%",
                    backgroundColor: "#ffffff",
                    cursor: "default",
                  }}
                  placeholder="Đang tải..."
                />
                <span
                  style={{
                    fontSize: 12,
                    color: "#6c757d",
                    fontWeight: "500",
                  }}
                >
                  ml/s
                </span>
              </div>
            </div>
          )}

          <p
            style={{
              fontSize: 13,
              color: "#45c3a1",
              fontWeight: "bold",
              margin: 0,
            }}
          >
            {calibrationInformation?.[indexOfPump]?.isPumpActivedBefore ? (
              <p style={{ color: "red" }}>
                `${functionsForControl.onOffCoil.label} đã hoạt động trước đó !`
              </p>
            ) : calibrationInformation?.[indexOfPump]?.fulfill ? (
              `${functionsForControl.onOffCoil.label} đang bơm đầy ống...`
            ) : calibrationInformation?.[indexOfPump]?.calibration ? (
              `${functionsForControl.onOffCoil.label} đang hiệu chuẩn...`
            ) : (
              ""
            )}
          </p>

          {calibrationInformation?.[indexOfPump]?.calibration && (
            <ModalPreventAction
              functionItem={functionItem}
              handleCancelCalibration={handleCancelCalibration}
              setIsCalibProgressFinished={setIsCalibProgressFinished}
              totalTime={Math.ceil(
                calibrationInformation?.[indexOfPump]?.totalTimeCalibration
              )}
              startTimestamp={
                calibrationInformation?.[indexOfPump]?.startTimestampCalibration
              }
            />
          )}

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              width: "100%",
              gap: 16,
            }}
          >
            {/* Step 1: Input actual ML value - Available during or after calibration run */}
            {(calibrationInformation?.[indexOfPump]?.calibration ||
              isCalibProgressFinished) &&
              !isCalibrationInProgress && (
                <div
                  style={{
                    marginTop: 16,
                    width: "100%",
                    display: "flex",
                    flexDirection: "column",
                    gap: 8,
                  }}
                >
                  <div
                    style={{
                      padding: 8,
                      backgroundColor: calibrationInformation?.[indexOfPump]
                        ?.calibration
                        ? "#fff7e6"
                        : "#f6ffed",
                      borderRadius: 4,
                      border: `1px solid ${
                        calibrationInformation?.[indexOfPump]?.calibration
                          ? "#ffd591"
                          : "#b7eb8f"
                      }`,
                    }}
                  >
                    <p
                      style={{
                        margin: 0,
                        fontSize: 12,
                        color: calibrationInformation?.[indexOfPump]
                          ?.calibration
                          ? "#d46b08"
                          : "#52c41a",
                        fontWeight: "bold",
                      }}
                    >
                      {calibrationInformation?.[indexOfPump]?.calibration
                        ? "💡 Bạn có thể nhập kết quả đo ngay bây giờ hoặc chờ hết thời gian"
                        : "✅ Đã hoàn thành chạy bơm, hãy nhập kết quả đo được"}
                    </p>
                  </div>

                  <p style={{ margin: 0, fontSize: 14, fontWeight: "bold" }}>
                    Bước 1: Nhập lượng thực tế đo được (ml)
                  </p>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 8,
                    }}
                  >
                    <InputNumberWithKeyboard
                      placeholder="Nhập số liệu thực tế (ml)"
                      style={{ width: "100%" }}
                      onChange={(value) => setActualMlValue(value as number)}
                      value={actualMlValue}
                    />
                    <Button
                      type="primary"
                      onClick={handleSetActualMl}
                      disabled={!actualMlValue}
                    >
                      Lưu
                    </Button>
                  </div>
                </div>
              )}

            {/* Step 2: Trigger calibration calculation */}
            {isCalibProgressFinished &&
              actualMlValue &&
              !isCalibrationInProgress && (
                <div
                  style={{
                    width: "100%",
                    display: "flex",
                    flexDirection: "column",
                    gap: 8,
                  }}
                >
                  <p style={{ margin: 0, fontSize: 14, fontWeight: "bold" }}>
                    Bước 2: Tính toán hệ số hiệu chuẩn mới
                  </p>
                  <Button
                    type="primary"
                    onClick={handleTriggerCalibration}
                    style={{ backgroundColor: "#52c41a" }}
                  >
                    Tính toán và cập nhật hệ số hiệu chuẩn
                  </Button>
                </div>
              )}

            {/* Calibration in progress indicator */}
            {isCalibrationInProgress && (
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 8,
                  padding: 16,
                  backgroundColor: "#f6ffed",
                  border: "1px solid #b7eb8f",
                  borderRadius: 8,
                }}
              >
                <p
                  style={{
                    margin: 0,
                    fontSize: 14,
                    fontWeight: "bold",
                    color: "#52c41a",
                  }}
                >
                  🔄 Đang tính toán hệ số hiệu chuẩn...
                </p>
                <p style={{ margin: 0, fontSize: 12, color: "#666" }}>
                  Đang xử lý và cập nhật giá trị mới
                </p>
              </div>
            )}
          </div>

          {/* Debug Information - Hidden in production */}
          {shouldShowDebugFeature("calibrationDebugInfo") && (
            <Collapse
              size="small"
              style={{ marginTop: 16 }}
              items={[
                {
                  key: "debug",
                  label: "🔧 Debug Info",
                  children: (
                    <CalibrationDebugInfo
                      pumpIndex={indexOfPump}
                      oldCalibValue={oldCalibValue}
                      latestHoldingValue={latestHoldingValue}
                      actualMlValue={actualMlValue}
                      isCalibrationInProgress={isCalibrationInProgress}
                      isCalibProgressFinished={isCalibProgressFinished}
                    />
                  ),
                },
              ]}
            />
          )}
        </div>

        <Modal
          title={"Bơm đầy ống"}
          open={openModal1}
          onOk={() => {
            setOpenModal1(false);
            setActiveModal(null);
          }}
          onCancel={() => {
            setOpenModal1(false);
            setActiveModal(null);
          }}
          maskClosable={false}
          okText={"Xác nhận"}
          okButtonProps={{ type: "default" }}
          cancelText={"Huỷ"}
          cancelButtonProps={{ hidden: true }}
          afterOpenChange={(open) => {
            if (open) {
              setActiveModal("fulfill");
              // Force refresh data when modal opens
              setRefreshTrigger((prev) => prev + 1);
            } else {
              setActiveModal(null);
            }
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: 16,
              marginBottom: 32,
            }}
          >
            <div
              style={{
                padding: 8,
                backgroundColor: "#f6ffed",
                borderRadius: 4,
              }}
            >
              <p
                style={{
                  margin: 0,
                  fontSize: 12,
                  color: "#52c41a",
                  fontWeight: "bold",
                }}
              >
                💡 Hướng dẫn: Nhập giá trị lớn (ví dụ: 1000ml) và bật bơm để đầy
                ống dẫn
              </p>
            </div>

            <DigitControl
              key={`fulfill-holding-${refreshTrigger}`}
              functionItem={functionsForControl.digitCoil}
              setLatestDigitValue={setLatestHoldingValue}
            />
            <OnOffControl
              functionItem={functionsForControl.onOffCoil}
              setResponseStatusOfOnOffControl={setResponseForFulfill}
            />
          </div>
        </Modal>

        <Modal
          title={"Tiến hành hiệu chuẩn"}
          open={openModal2}
          onOk={() => {
            setOpenModal2(false);
            setActiveModal(null);
          }}
          onCancel={() => {
            setOpenModal2(false);
            setActiveModal(null);
          }}
          maskClosable={false}
          okText={"Xác nhận"}
          okButtonProps={{ type: "default" }}
          cancelText={"Huỷ"}
          cancelButtonProps={{ hidden: true }}
          afterOpenChange={(open) => {
            if (open) {
              setActiveModal("calibration");
              // Force refresh data when modal opens
              setRefreshTrigger((prev) => prev + 1);
            } else {
              setActiveModal(null);
            }
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: 16,
              marginBottom: 32,
            }}
          >
            <div
              style={{
                padding: 8,
                backgroundColor: "#fff7e6",
                borderRadius: 4,
              }}
            >
              <p
                style={{
                  margin: 0,
                  fontSize: 12,
                  color: "#d46b08",
                  fontWeight: "bold",
                }}
              >
                ⚠️ Lưu ý: Kiểm tra và điều chỉnh các thông số trước khi bắt đầu
                hiệu chuẩn
              </p>
            </div>

            {/* Lưu lượng bơm (HOLDING_SETML_BOM_n) */}
            <DigitControl
              key={`holding-${refreshTrigger}`}
              functionItem={functionsForControl.digitCoil}
              setLatestDigitValue={setLatestHoldingValue}
            />

            {/* Hệ số hiệu chuẩn cũ (HOLDING_CALIB_BOM_n) */}
            {functionForOldCalibration && (
              <DigitControl
                key={`calib-${refreshTrigger}`}
                functionItem={functionForOldCalibration}
                setLatestDigitValue={(value) => setOldCalibValue(value)}
              />
            )}

            <OnOffControl
              functionItem={functionsForControl.onOffCoil}
              setResponseStatusOfOnOffControl={setResponseForCalibration}
            />
          </div>
        </Modal>
      </div>
    );
};

export default CalibPump;
