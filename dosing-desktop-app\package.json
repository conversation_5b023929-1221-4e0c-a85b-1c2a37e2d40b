{"private": true, "name": "dosing-desktop-app", "description": "<PERSON><PERSON>pp", "version": "1.0.0", "author": "VIIS <<EMAIL>>", "main": "app/background.js", "scripts": {"dev": "nextron", "postinstall": "electron-builder install-app-deps", "build": "nextron build", "build:mac": "nextron build --mac", "build:mac:universal": "nextron build --mac --universal", "build:linux": "nextron build --linux", "build:linux:arm64": "nextron build --linux --arm64", "build:linux:armv7l": "nextron build --linux --armv7l", "build:win32": "nextron build --win --ia32", "build:win64": "nextron build --win --x64"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "dayjs": "^1.11.13", "electron-serve": "^1.3.0", "electron-store": "^8.2.0", "eventemitter3": "^5.0.1", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "mqtt": "^5.13.0", "next-transpile-modules": "^10.0.1", "react-simple-keyboard": "^3.8.65", "simple-keyboard": "^3.8.50", "umi-request": "^1.4.0", "uplot": "^1.6.32", "uplot-react": "^1.2.2", "zustand": "^5.0.4"}, "devDependencies": {"@ant-design/icons": "^5.3.6", "@types/node": "^20.11.16", "@types/react": "^18.2.52", "antd": "^5.24.7", "electron": "^34.0.0", "electron-builder": "^24.13.3", "next": "^14.2.4", "nextron": "^9.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.7.3"}}