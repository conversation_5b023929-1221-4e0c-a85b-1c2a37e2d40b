import { FC, useState } from "react";
import { ProgramExecutionHistoryType } from "../../../services/program-execution-history";
import { Col, Divider, Modal, Row } from "antd";
import dayjs from "dayjs";
import {
  FieldTimeOutlined,
  CalendarOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { calculateSecond } from "../schedule-plan/ProgramContainer";

interface PEHContainerProps {
  key: any;
  stt: number;
  pehData: ProgramExecutionHistoryType;
}

const PEHContainer: FC<PEHContainerProps> = ({ key, stt, pehData }) => {
  const [showModal, setShowModal] = useState(false);
  const [typeOfData, setTypeOfData] = useState<any[]>(null);

  const handleOpenModal = (data: any[]) => {
    setTypeOfData(data);
    setShowModal(true);
  };

  const ShowSpecificInsights = () => {
    return (
      <Modal
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={false}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            overflowY: "scroll",
            gap: 8,
            height: 300,
          }}
        >
          {typeOfData?.map((item: any, index: number) => (
            <div
              key={index}
              style={{
                border: "1px solid #ddd",
                padding: 8,
                borderRadius: 16,
                backgroundColor: "rgb(250,250,250)",
              }}
            >
              <p
                style={{ margin: 0, fontSize: 15, color: "rgb(193, 150, 64)" }}
              >
                {item?.message}
              </p>
              <p
                style={{ margin: 0, fontSize: 13, color: "rgb(225, 172, 65)" }}
              >
                <FieldTimeOutlined />{" "}
                {dayjs(item?.created_at).format("DD/MM/YYYY, HH:mm:ss")}
              </p>
            </div>
          ))}
        </div>
      </Modal>
    );
  };
  return (
    <Row
      key={key}
      gutter={[16, 16]}
      style={{
        backgroundColor: "#fff",
        padding: 8,
        marginTop: 8,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Col span={2}>{stt}</Col>
      <Col span={8}>
        <p
          style={{
            color: "rgb(40,40,40)",
            fontWeight: "bold",
          }}
        >
          {pehData.label}
        </p>
        <p style={{ fontSize: 13, color: "rgb(100,100,100)" }}>
          <FieldTimeOutlined />{" "}
          {dayjs(pehData.log_creation).format("DD/MM/YYYY")},{" "}
          {pehData.start_time} - {pehData.end_time}
        </p>
        {/* <p style={{ fontSize: 13, color: "rgb(140,140,140)" }}>
          Kế hoạch thực hiện: Từ ngày{" "}
          <strong>{dayjs(pehData.start_date).format("DD/MM/YYYY")}</strong> đến
          ngày <strong>{dayjs(pehData.end_date).format("DD/MM/YYYY")}</strong>,
          Lúc {pehData.start_time} - {pehData.end_time}
        </p> */}
      </Col>

      <Col span={8}>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ color: "rgb(100,100,100)" }}>Tổng thời gian (giây)</p>
          <p style={{ color: "#45c3a1", fontWeight: "bold" }}>
            {calculateSecond(pehData.start_time, pehData.end_time)}
          </p>
        </div>
      </Col>

      <Col span={6}>
        {
          pehData?.errors?.length > 0 ? (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p
                style={{ cursor: "pointer", color: "rgb(68, 152, 185)" }}
                onClick={() => handleOpenModal(pehData?.errors)}
              >
                <WarningOutlined /> Lỗi cần kiểm tra: {pehData?.errors?.length}
              </p>
            </div>
          ) : pehData?.warnings?.length > 0 ? (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p
                style={{ cursor: "pointer", color: "rgb(225, 172, 65)" }}
                onClick={() => handleOpenModal(pehData?.warnings)}
              >
                <WarningOutlined /> Cảnh báo cần kiểm tra:{" "}
                {pehData?.warnings?.length}
              </p>
            </div>
          ) : pehData?.notifications?.length > 0 ? (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p
                style={{ cursor: "pointer", color: "rgb(68, 152, 185)" }}
                onClick={() => handleOpenModal(pehData?.notifications)}
              >
                <WarningOutlined /> Thông báo cần kiểm tra:{" "}
                {pehData?.notifications?.length}
              </p>
            </div>
          ) : null
          // <p style={{ color: "rgb(164, 215, 177)", fontWeight: "bold" }}>
          //   Không có lỗi, cảnh báo nào!
          // </p>
        }
        <ShowSpecificInsights />
      </Col>
    </Row>
  );
};

export default PEHContainer;
