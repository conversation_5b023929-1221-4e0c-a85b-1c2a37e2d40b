import { FC, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pop<PERSON> } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import CreateSchedulePlan from "./CreateSchedulePlan";
import CreateProgram from "./CreateProgram";

const CreateSchedulePlanOrProgram: FC = () => {
  const [openDrawer, setOpenDrawer] = useState<null | "plan" | "program">(null);

  const handleOpenPlanDrawer = () => {
    setOpenDrawer("plan");
  };

  const handleOpenProgramDrawer = () => {
    setOpenDrawer("program");
  };

  const handleCloseDrawer = () => {
    setOpenDrawer(null);
  };
  return (
    <div>
      {/* <Popover
        content={
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "start",
              gap: 8,
            }}
          >
            <Button
              type="primary"
              color="green"
              style={{
                width: "100%",
              }}
              onClick={handleOpenProgramDrawer}
            >
              <PERSON><PERSON><PERSON>ng trình mẫu
            </Button>
            <Divider
              children={
                <p style={{ fontSize: 13, color: "gray", margin: 0 }}>Hoặc</p>
              }
            />
            <Button
              type="primary"
              color="green"
              style={{
                width: "100%",
              }}
              onClick={handleOpenPlanDrawer}
            >
              Kế hoạch
            </Button>
          </div>
        }
      >
        <Button
          type="primary"
          color="green"
          style={{
            position: "fixed",
            bottom: 16,
            right: 16,
            boxShadow: "0px 2px 50px 2px rgba(0, 0, 0, 0.5)",
          }}
          icon={<PlusOutlined />}
          size="large"
        />
      </Popover> */}

      <Button
        type="primary"
        style={{
          position: "fixed",
          bottom: 16,
          right: 16,
          boxShadow: "0px 2px 50px 2px rgba(0, 0, 0, 0.5)",
        }}
        icon={<PlusOutlined />}
        size="large"
        onClick={handleOpenPlanDrawer}
      />

      <Drawer
        width={"75%"}
        title="Tạo kế hoạch mới"
        placement="right"
        onClose={handleCloseDrawer}
        open={openDrawer === "plan"}
      >
        <CreateSchedulePlan onClose={handleCloseDrawer} />
      </Drawer>

      {/* <Drawer
        width={"75%"}
        title="Tạo chương trình mẫu"
        placement="right"
        onClose={handleCloseDrawer}
        open={openDrawer === "program"}
      >
        <CreateProgram onClose={handleCloseDrawer} deviceId={deviceId} schedulePlanId={schedulePlanId} />
      </Drawer> */}
    </div>
  );
};

export default CreateSchedulePlanOrProgram;
