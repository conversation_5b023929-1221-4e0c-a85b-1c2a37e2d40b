import { TypeOfLanguage } from "./typeOfLanguage";

export const vi_VN: TypeOfLanguage = {
  "common.login.success": "<PERSON><PERSON><PERSON> nhập thành công",
  "common.login.error": "<PERSON><PERSON><PERSON> nhập thất bại",
  "common.logout.success": "Đăng xuất thành công",
  "common.calibsensors.select_input_type": "Chọn loại ",
  "common.calibsensors.input.card": "Số liệu ghi nhận",
  "common.calibsensors.input.from_user": "Từ người dùng",
  "common.calibsensors.input.from_device": "Từ thiết bị",
  "common.calibsensors.visualization.card": "Biểu đồ",
  "common.calibsensors.table.card": "Lịch sử ghi nhận",
  "common.control.switch.on": "Bật",
  "common.control.switch.off": "Tắt",
  "common.control.post.success": "<PERSON><PERSON><PERSON><PERSON> khiển thành công",
  "common.control.post.error": "Điều khiển thất bại",
  "common.control.tab.config.config": "<PERSON><PERSON>u hình",
};
