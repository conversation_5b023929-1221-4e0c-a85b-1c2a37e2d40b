import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

interface DeviceEcPhTempState {
  ec: string;
  ph: string;
  temp: string;
  setEc: (ec: string) => void;
  setPh: (ph: string) => void;
  setTemp: (temp: string) => void;
}

const useDeviceEcPhTempStore = create(
  immer<DeviceEcPhTempState>((set, get) => ({
    ec: "0",
    ph: "0",
    temp: "0",
    setEc: (ec: string) => set({ ec: ec }),
    setPh: (ph: string) => set({ ph: ph }),
    setTemp: (temp: string) => set({ temp: temp }),
  }))
);
export default useDeviceEcPhTempStore;
