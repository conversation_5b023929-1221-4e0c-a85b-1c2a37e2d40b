# Hướng dẫn sử dụng Debug Mode cho Calibration

## 🎯 Tổng quan

Debug mode giúp ẩn/hiện các component debug trong ứng dụng calibration để có giao diện sạch sẽ cho production và đầy đủ tính năng cho development.

## 🔧 Cách hoạt động

### 1. **Automatic Mode (Mặc định)**
```typescript
// Tự động ẩn trong production, hiện trong development
process.env.NODE_ENV === 'development' // true = hiện debug
```

### 2. **Manual Mode (Có thể toggle)**
```typescript
// C<PERSON> thể bật/tắt thủ công bất kể environment
DEBUG_CONFIG.isDebugEnabled = true/false
```

### 3. **Feature-specific Control**
```typescript
// Kiểm soát từng tính năng debug riêng biệt
DEBUG_CONFIG.features = {
  calibrationDebugInfo: false,    // Debug info trong mỗi pump
  calibrationTestTool: false,     // Test tool trong container
  apiLogger: false,               // API call logger
  consoleLogging: true,           // Console debug logs
}
```

## 📱 Cách sử dụng

### **Phương pháp 1: Sử dụng UI Control Panel**

1. **Mở trang Calibration**
2. **Tìm "🔧 Debug Control Panel"** (chỉ hiện khi debug available)
3. **Toggle Master Debug** để bật/tắt debug mode
4. **Toggle từng feature** theo nhu cầu

```
┌─────────────────────────────────┐
│     🔧 Debug Control Panel      │
│                                 │
│ Master Debug Control            │
│ [ON] Debug Mode: Enabled        │
│                                 │
│ Debug Features                  │
│ [OFF] Calibration Debug Info    │
│ [OFF] Calibration Test Tool     │
│ [OFF] API Logger                │
│ [ON]  Console Logging           │
└─────────────────────────────────┘
```

### **Phương pháp 2: Sử dụng Console Commands**

Mở **Developer Tools Console** và sử dụng:

```javascript
// Bật/tắt debug mode
window.calibrationDebug.toggle()

// Bật/tắt feature cụ thể
window.calibrationDebug.toggleFeature('calibrationDebugInfo')
window.calibrationDebug.toggleFeature('calibrationTestTool')
window.calibrationDebug.toggleFeature('apiLogger')
window.calibrationDebug.toggleFeature('consoleLogging')

// Xem config hiện tại
window.calibrationDebug.config

// Debug log
window.calibrationDebug.log('Test message', { data: 'example' })
```

## 🎛️ Debug Components

### 1. **Calibration Debug Info** (`calibrationDebugInfo`)
- **Vị trí**: Trong mỗi CalibPump component
- **Nội dung**: 
  - Keys sẽ được gửi
  - Giá trị hiện tại
  - Công thức tính toán
  - Trạng thái process

### 2. **Calibration Test Tool** (`calibrationTestTool`)
- **Vị trí**: Trong CalibContainer, tab "🧪 Test Tool"
- **Nội dung**:
  - Test API calls
  - MQTT subscription test
  - Manual input testing

### 3. **API Logger** (`apiLogger`)
- **Vị trí**: Sẽ được implement trong tương lai
- **Nội dung**: Log tất cả API calls với timeline

### 4. **Console Logging** (`consoleLogging`)
- **Vị trí**: Browser console
- **Nội dung**: Debug messages với prefix `[CALIBRATION_DEBUG]`

## 🚀 Use Cases

### **Production Environment**
```typescript
// Tất cả debug components bị ẩn
process.env.NODE_ENV = 'production'
DEBUG_CONFIG.isDebugEnabled = false
→ Giao diện sạch sẽ, chỉ có chức năng chính
```

### **Development Environment**
```typescript
// Debug Control Panel xuất hiện
process.env.NODE_ENV = 'development'
→ Có thể bật/tắt debug features theo nhu cầu
```

### **Troubleshooting in Production**
```javascript
// Bật debug tạm thời để troubleshoot
window.calibrationDebug.toggle()
window.calibrationDebug.toggleFeature('calibrationDebugInfo')
→ Xem debug info để tìm lỗi
→ Tắt lại khi xong
```

### **Testing New Features**
```javascript
// Bật test tool để test API calls
window.calibrationDebug.toggleFeature('calibrationTestTool')
→ Sử dụng test tool
→ Tắt khi không cần
```

## 📋 Debug States

### **State 1: All Hidden (Production)**
```
✅ Calibration UI (clean)
❌ Debug Control Panel
❌ Debug Info in pumps
❌ Test Tool
❌ Console logs
```

### **State 2: Debug Available (Development)**
```
✅ Calibration UI
✅ Debug Control Panel
❌ Debug Info in pumps (off by default)
❌ Test Tool (off by default)
✅ Console logs (on by default)
```

### **State 3: Full Debug (Manual)**
```
✅ Calibration UI
✅ Debug Control Panel
✅ Debug Info in pumps
✅ Test Tool
✅ Console logs
✅ API Logger (future)
```

## 🔍 Implementation Details

### **File Structure**
```
utils/debugMode.ts           - Debug configuration & functions
DebugControlPanel.tsx        - UI control panel
CalibPump.tsx               - Uses shouldShowDebugFeature()
CalibContainer.tsx          - Uses shouldShowDebug()
CalibrationDebugInfo.tsx    - Debug info component
CalibrationTest.tsx         - Test tool component
```

### **Key Functions**
```typescript
shouldShowDebug()                    // Master debug check
shouldShowDebugFeature(feature)      // Feature-specific check
toggleDebugMode()                    // Toggle master debug
toggleDebugFeature(feature)          // Toggle specific feature
debugLog(message, data)              // Debug logging
```

### **Environment Detection**
```typescript
DEBUG_CONFIG.isDevelopment = process.env.NODE_ENV === 'development'
```

## ⚠️ Important Notes

### 1. **Performance**
- Debug components chỉ render khi cần thiết
- Không ảnh hưởng performance khi tắt

### 2. **Security**
- Debug mode không expose sensitive data
- Chỉ hiển thị thông tin technical

### 3. **Persistence**
- Debug settings không được lưu
- Reset về default khi refresh page

### 4. **Global Access**
- Console commands available globally
- Dễ dàng toggle từ DevTools

## 🎯 Best Practices

### **For Developers**
1. **Development**: Bật debug features khi cần
2. **Testing**: Sử dụng test tool để verify API calls
3. **Debugging**: Bật console logging để track issues

### **For Production**
1. **Default**: Tất cả debug tắt
2. **Troubleshooting**: Bật tạm thời qua console
3. **Clean up**: Tắt debug sau khi xong

### **For Users**
1. **Normal use**: Không thấy debug components
2. **Support**: Có thể bật debug khi được hướng dẫn
3. **Clean UI**: Giao diện sạch sẽ, tập trung vào chức năng chính

## 🚀 Future Enhancements

### **Planned Features**
- **API Logger**: Timeline của tất cả API calls
- **Performance Monitor**: Track render times
- **State Inspector**: Xem real-time state changes
- **Export Debug Data**: Export logs để support

### **Possible Improvements**
- **Persistent Settings**: Lưu debug preferences
- **Remote Debug**: Enable debug từ admin panel
- **Debug Levels**: Different levels of debug detail
- **Custom Debug Views**: Tùy chỉnh debug display

## 📞 Support

### **Console Commands Quick Reference**
```javascript
// Quick toggle debug
window.calibrationDebug.toggle()

// Show all available commands
console.log(window.calibrationDebug)

// Current config
window.calibrationDebug.config
```

### **Common Issues**
1. **Debug panel không hiện**: Check environment hoặc toggle manual
2. **Features không hoạt động**: Check master debug enabled
3. **Console commands không có**: Refresh page để load debug utils
