import React, { useEffect, useState } from "react";
import <PERSON> from "next/head";
import Link from "next/link";
import {
  Layout,
  Form,
  Select,
  InputNumber,
  DatePicker,
  Switch,
  Slider,
  <PERSON>ton,
  Tabs,
} from "antd";
import InputTextWithKeyboard from "../../components/virtual-input/InputTextWithKeyboard";
import InputNumberWithKeyboard from "../../components/virtual-input/InputNumberWithKeyboard";
import useDeviceDataStore from "../../stores/deviceDataStore";
import TabPane from "antd/es/tabs/TabPane";
import DeviceControl from "../../components/control";
import { FunctionList } from "../../services/device/devices";

const { Header, Content } = Layout;

const { Item: FormItem } = Form;
const { Option } = Select;

export default function SettingPage() {
  const { functionListForControl } = useDeviceDataStore();
  const [activeGroup, setActiveGroup] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>(null);

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        padding: 16,
      }}
    >
      <p style={{ fontSize: 24, fontWeight: "bold" }}>Danh sách cài đặt</p>
      <div style={{ display: "flex", flexDirection: "row" }}>
        <Tabs
          defaultActiveKey="1"
          tabPosition="top"
          tabBarGutter={0}
          tabBarStyle={{ overflow: "scroll" }}
          style={{
            height: "440px",
            overflow: "scroll",
            width: "35%",
            paddingRight: 16,
            borderRight: "1px solid #ddd",
          }}
        >
          {functionListForControl.map((tabItem: any) =>
            tabItem?.children?.length === 0 ||
            (!tabItem?.identifier?.includes("rotect") &&
              !tabItem?.identifier?.includes("onfig")) ? null : (
              <TabPane
                key={tabItem.label}
                tab={
                  <div
                    style={{
                      backgroundColor: "#fff",
                      padding: 8,
                      border: "1px solid #ddd",
                      borderRadius: 8,
                      marginRight: 16,
                      textAlign: "left",
                      background: "rgb(183, 255, 203)",
                    }}
                  >
                    {tabItem.label}
                  </div>
                }
              >
                {tabItem.children.map((groupItem: any) =>
                  groupItem?.children?.length === 0 ? null : (
                    <div
                      key={groupItem.label}
                      style={{
                        marginBottom: 16,
                        padding: 8,
                        border: "1px solid #ddd",
                        borderRadius: 8,
                        background:
                          activeGroup === groupItem.label
                            ? "#45c3a1"
                            : "linear-gradient(to right,rgba(200,200,200,0.2),#fff)",
                        color:
                          activeGroup === groupItem.label ? "#fff" : "#000",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        setActiveGroup(
                          activeGroup === groupItem.label
                            ? null
                            : groupItem.label
                        );
                        setActiveTab(groupItem.label);
                      }}
                    >
                      <p style={{ fontWeight: "bold", margin: 0 }}>
                        {groupItem.label}
                      </p>
                    </div>
                  )
                )}
              </TabPane>
            )
          )}
        </Tabs>
        {activeGroup && (
          <div style={{ flex: 1, marginLeft: 16 }}>
            <Tabs
              activeKey={activeTab}
              onChange={(key) => setActiveTab(key)}
              tabPosition="top"
              tabBarGutter={0}
              tabBarStyle={{ overflow: "scroll" }}
              style={{ height: "440px" }}
            >
              {functionListForControl
                .flatMap((tabItem: any) => tabItem.children)
                .filter((groupItem: any) => groupItem.label === activeGroup)
                .map((groupItem: any) => (
                  <TabPane key={groupItem.label} tab={groupItem.label}>
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                        overflow: "scroll",
                        height: "360px",
                      }}
                    >
                      {groupItem.children.map((functionItem: FunctionList) => (
                        <DeviceControl
                          key={functionItem.name}
                          functionItem={functionItem}
                        />
                      ))}
                    </div>
                  </TabPane>
                ))}
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
}
