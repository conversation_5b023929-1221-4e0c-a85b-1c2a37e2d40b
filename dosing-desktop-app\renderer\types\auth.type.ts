export type IIotCustomerUser = {
  name: string;
  creation: any;
  modified: string;
  modified_by: string;
  description: string;
  is_deactivated: boolean;
  owner: any;
  docstatus: number;
  idx: number;
  user_id: any;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  id: any;
  user_name: any;
  created_time: any;
  iot_customer_id: any;
  user_avatar: any;
  email: string;
  full_name: any;
  phone_number: any;
  address: any;
  date_join: any;
  date_active: any;
  date_warranty: any;
  customer_id: string;
  first_name: string;
  lats_name: any;
  last_name: string;
  district: any;
  ward: any;
  province: any;
  is_admin: number;
  iot_dynamic_role: string;
};


/**
* Generate by /home/<USER>/VIIS/iot-backend-typescript/tools/gen_type.js
*/
export class IIotCustomer {
  name!: string;
  creation?: string
  modified?: string
  customer_name?: string; // Data
  created_time?: string; // Date
  email?: string; // Data
  phone?: string; // Data
  description?: string; // Data
  address?: string; // Data
  city?: string; // Data
  country?: string; // Data
  province?: string; // Data
  zip_code?: string; // Data
  logo?: string; // Attach Image
  district?: string; // Data
  ward?: string; // Data
  type?: "group" | "individual"; // group|individual
  developer_mode?: number; // Check
  is_receive_connection_noti?: number; // Check
  is_receive_notification_noti?: number; // Check
}


export enum RoleEnum {
  TECHNICIAN_EMPLOYEE = 'TECHNICIAN_EMPLOYEE',
  CUSTOMER_ADMIN = 'CUSTOMER_ADMIN',
  ADMIN_WAREHOUSE = 'ADMIN_WAREHOUSE',
}

export enum SECTION_PERMISSION {
  //SYSTEM ADMIN
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  // CROP
  CROP_CREATE = 'CROP_CREATE',
  CROP_READ = 'CROP_READ',
  CROP_UPDATE = 'CROP_UPDATE',
  CROP_DELETE = 'CROP_DELETE',

  // TASK
  TASK_CREATE = 'TASK_CREATE',
  TASK_READ = 'TASK_READ',
  TASK_UPDATE = 'TASK_UPDATE',
  TASK_DELETE = 'TASK_DELETE',

  // PLAN
  PLAN_CREATE = 'PLAN_CREATE',
  PLAN_READ = 'PLAN_READ',
  PLAN_UPDATE = 'PLAN_UPDATE',
  PLAN_DELETE = 'PLAN_DELETE',

  // STATE
  STATE_CREATE = 'STATE_CREATE',
  STATE_READ = 'STATE_READ',
  STATE_UPDATE = 'STATE_UPDATE',
  STATE_DELETE = 'STATE_DELETE',

  // PLANT
  PLANT_CREATE = 'PLANT_CREATE',
  PLANT_READ = 'PLANT_READ',
  PLANT_UPDATE = 'PLANT_UPDATE',
  PLANT_DELETE = 'PLANT_DELETE',

  // PROJECT
  PROJECT_CREATE = 'PROJECT_CREATE',
  PROJECT_READ = 'PROJECT_READ',
  PROJECT_UPDATE = 'PROJECT_UPDATE',
  PROJECT_DELETE = 'PROJECT_DELETE',

  // ZONE
  ZONE_CREATE = 'ZONE_CREATE',
  ZONE_READ = 'ZONE_READ',
  ZONE_UPDATE = 'ZONE_UPDATE',
  ZONE_DELETE = 'ZONE_DELETE',

  // CATEGORY
  CATEGORY_CREATE = 'CATEGORY_CREATE',
  CATEGORY_READ = 'CATEGORY_READ',
  CATEGORY_UPDATE = 'CATEGORY_UPDATE',
  CATEGORY_DELETE = 'CATEGORY_DELETE',

  // PRODUCT
  PRODUCT_CREATE = 'PRODUCT_CREATE',
  PRODUCT_READ = 'PRODUCT_READ',
  PRODUCT_UPDATE = 'PRODUCT_UPDATE',
  PRODUCT_DELETE = 'PRODUCT_DELETE',

  // STORAGE
  STORAGE_CREATE = 'STORAGE_CREATE',
  STORAGE_READ = 'STORAGE_READ',
  STORAGE_UPDATE = 'STORAGE_UPDATE',
  STORAGE_DELETE = 'STORAGE_DELETE',

  // CATEGORY_INVENTORY
  CATEGORY_INVENTORY_CREATE = 'CATEGORY_INVENTORY_CREATE',
  CATEGORY_INVENTORY_READ = 'CATEGORY_INVENTORY_READ',
  CATEGORY_INVENTORY_UPDATE = 'CATEGORY_INVENTORY_UPDATE',
  CATEGORY_INVENTORY_DELETE = 'CATEGORY_INVENTORY_DELETE',

  //CATEGORY_INVENTORY_FIELD_LEVEL
  CATEGORY_INVENTORY_FIELD_LEVEL_READ = 'CATEGORY_INVENTORY_FIELD_LEVEL_READ',
  CATEGORY_INVENTORY_FIELD_LEVEL_WRITE = 'CATEGORY_INVENTORY_FIELD_LEVEL_WRITE',

  // PRODUCT_INVENTORY
  PRODUCT_INVENTORY_CREATE = 'PRODUCT_INVENTORY_CREATE',
  PRODUCT_INVENTORY_READ = 'PRODUCT_INVENTORY_READ',
  PRODUCT_INVENTORY_UPDATE = 'PRODUCT_INVENTORY_UPDATE',
  PRODUCT_INVENTORY_DELETE = 'PRODUCT_INVENTORY_DELETE',

  // EMPLOYEE
  EMPLOYEE_CREATE = 'EMPLOYEE_CREATE',
  EMPLOYEE_READ = 'EMPLOYEE_READ',
  EMPLOYEE_UPDATE = 'EMPLOYEE_UPDATE',
  EMPLOYEE_DELETE = 'EMPLOYEE_DELETE',

  // DYNAMIC ROLE
  DYNAMIC_ROLE_CREATE = 'DYNAMIC_ROLE_CREATE',
  DYNAMIC_ROLE_READ = 'DYNAMIC_ROLE_READ',
  DYNAMIC_ROLE_UPDATE = 'DYNAMIC_ROLE_UPDATE',
  DYNAMIC_ROLE_DELETE = 'DYNAMIC_ROLE_DELETE',

  // TIMEKEEPING
  TIMEKEEPING_CREATE = 'TIMEKEEPING_CREATE',
  TIMEKEEPING_READ = 'TIMEKEEPING_READ',
  TIMEKEEPING_UPDATE = 'TIMEKEEPING_UPDATE',
  TIMEKEEPING_DELETE = 'TIMEKEEPING_DELETE',

  //VISITOR
  VISITOR_CREATE = 'VISITOR_CREATE',
  VISITOR_READ = 'VISITOR_READ',
  VISITOR_UPDATE = 'VISITOR_UPDATE',
  VISITOR_DELETE = 'VISITOR_DELETE',

  //IOT_DEVICE
  IOT_DEVICE_CREATE = 'IOT_DEVICE_CREATE',
  IOT_DEVICE_READ = 'IOT_DEVICE_READ',
  IOT_DEVICE_UPDATE = 'IOT_DEVICE_UPDATE',
  IOT_DEVICE_DELETE = 'IOT_DEVICE_DELETE',
}

export function listEnumValues<T extends Record<string, string>>(enumObject: T): string[] {
  return (Object.keys(enumObject) as Array<keyof T>).map((key) => enumObject[key]);
}

export type ITokenInfo = {
  first_name: string;
  last_name: string;
  email: string;
  user_type: string;
  user_id: string;
  user_role: RoleEnum[];
  sections: string;
  is_admin: number;
  customer_id: string;
  credential_id: string;
  enable: number;
  iat: number;
  exp: number;
  subscriptions: string[];
};
