// import { useModel } from '@umijs/max';
import { OnMessageCallback } from "mqtt";
import { useEffect, useMemo, useState } from "react";

//
// Thay vì dùng useModel của UmiJS, ta sẽ dùng useMqttStore
//

export const useDeviceOnlineChange = (params: {
  deviceId?: string;
  initOnline?: boolean;
}) => {
  const [isOnline, setIsOnline] = useState(!!params.initOnline);
  // const { mqttClient } = useModel('MQTTNotification');
  const handleMessageMQTT: OnMessageCallback = (topic, msg) => {
    try {
      const dataMqtt: any[] = JSON.parse(msg.toString());
      if (Array.isArray(dataMqtt)) {
        const deviceIdThingsBoard =
          topic.split("/")[topic.split("/").length - 2];

        if (deviceIdThingsBoard === params.deviceId) {
          const dataOnline = dataMqtt.find((item) => item.key === "online");
          if (dataOnline) {
            setIsOnline(!!dataOnline?.value);
          }
        }
      }
    } catch (error) {
      console.log("error", error);
    }
  };
  // change online
  useEffect(() => {
    setIsOnline(!!params.initOnline);
  }, [params.initOnline]);
  // useEffect(() => {
  //   if (mqttClient.connected && params.deviceId) {
  //     mqttClient.on('message', handleMessageMQTT);
  //   }
  //   return () => {
  //     mqttClient.off('message', handleMessageMQTT);
  //   };
  // }, [mqttClient.connected, params.deviceId]);
  return useMemo(
    () => ({
      isOnline: params.deviceId ? isOnline : false, // nếu có device id
    }),
    [isOnline, params.deviceId]
  );
};
