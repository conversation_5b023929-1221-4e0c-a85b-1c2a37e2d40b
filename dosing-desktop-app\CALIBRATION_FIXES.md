# Sửa lỗi Calibration và Hướng dẫn Test

## 🐛 Lỗi đã sửa

### 1. TypeError: handleMessage is not a function

**Nguyên nhân**: `handleMessage` trong MQTT store không phải là function mà là object.

**Giải pháp**: Sử dụng `subscribe` và `unsubscribe` thay vì `handleMessage`.

#### Trước (lỗi):
```typescript
const { handleMessage } = useMqttStore();
const unsubscribe = handleMessage((topic, message) => {
  // ...
});
```

#### Sau (đã sửa):
```typescript
const { subscribe, unsubscribe } = useMqttStore();
const subscriptionIds = subscribe([genDeviceTopic(deviceId)], (message) => {
  // ...
});

return () => {
  if (subscriptionIds.length > 0) {
    unsubscribe(subscriptionIds);
  }
};
```

### 2. Thi<PERSON><PERSON> ô nhập HOLDING_SETML_BOM_x

**Giải pháp**: Đã có sẵn trong `DigitControl` component với `functionsForControl.digitCoil`.

**Mapping**:
- `functionsForControl.digitCoil` = `HOLDING_SETML_BOM_x`
- `functionsForControl.onOffCoil` = `COIL_BOM_x`

## 🧪 Test Tool

Đã thêm `CalibrationTest` component để test các chức năng:

### Chức năng test:
1. **Test Send Actual ML**: Gửi `CALIB_ACTUAL_ML_BOM_n`
2. **Test Trigger Calibration**: Gửi `CALCULATE_CALIB_BOM_n = true`
3. **Test MQTT Subscription**: Subscribe và hiển thị messages

### Cách sử dụng:
1. Mở trang Calibration
2. Expand "🧪 Test Tool"
3. Chọn pump index (1-14)
4. Nhập giá trị test
5. Nhấn các button test

## 📋 Checklist Test

### 1. Test API Calls
- [ ] Test Send Actual ML thành công
- [ ] Test Trigger Calibration thành công
- [ ] Kiểm tra console logs
- [ ] Kiểm tra Network tab trong DevTools

### 2. Test MQTT
- [ ] MQTT Connected = ✅
- [ ] Test MQTT Subscription nhận được messages
- [ ] Kiểm tra topic: `viis/things/v2/{deviceId}/telemetry`

### 3. Test Full Flow
- [ ] Bơm đầy ống hoạt động
- [ ] Tiến hành hiệu chuẩn hoạt động
- [ ] Nhập actual ML thành công
- [ ] Trigger calibration thành công
- [ ] Nhận được notification hoàn thành

## 🔧 Debug Information

### Console Logs cần kiểm tra:
```javascript
// API calls
✅ Sent CALIB_ACTUAL_ML_BOM_1 = 120
✅ Sent CALCULATE_CALIB_BOM_1 = true

// MQTT messages
📨 MQTT Message received: [{"ts":1234567890,"key":"CALCULATE_CALIB_BOM_1","value":false}]
🔧 Calibration flags found: [{"key":"CALCULATE_CALIB_BOM_1","value":false}]

// Subscription
Subscribing to topic: viis/things/v2/{deviceId}/telemetry
Subscription IDs: ["abc123"]
```

### Network Requests cần kiểm tra:
```
POST /api/v2/thingsboard/rpc/oneway/{deviceId}
Body: {
  "method": "set_state",
  "params": {
    "CALIB_ACTUAL_ML_BOM_1": 120
  },
  "timeout": 10000
}
```

## 🎯 Expected Behavior

### 1. Khi nhấn "Lưu" (actual ML):
- Gửi API call với `CALIB_ACTUAL_ML_BOM_n`
- Hiển thị success message
- Enable button "Tính toán"

### 2. Khi nhấn "Tính toán":
- Gửi API call với `CALCULATE_CALIB_BOM_n = true`
- Hiển thị "🔄 Đang tính toán..."
- Subscribe MQTT để monitor

### 3. Khi Node-RED hoàn thành:
- MQTT nhận được `CALCULATE_CALIB_BOM_n = false`
- Ẩn "Đang tính toán..."
- Hiển thị "Hiệu chuẩn hoàn tất!"

## 🚨 Troubleshooting

### Lỗi MQTT không kết nối:
1. Kiểm tra MQTT broker URL
2. Kiểm tra credentials
3. Kiểm tra network connectivity

### Lỗi API calls:
1. Kiểm tra deviceId có đúng không
2. Kiểm tra token authentication
3. Kiểm tra backend server

### Lỗi không nhận được MQTT response:
1. Kiểm tra Node-RED có chạy không
2. Kiểm tra Node-RED logs
3. Kiểm tra global variables trong Node-RED

## 📝 Node-RED Debug

### Kiểm tra Node-RED logs:
```
[PUMP_CALIB_DEBUG] Starting pump calibration check...
[PUMP_CALIB_DEBUG] Global data loaded - configKeys: X, holdingData: Y
[PUMP_CALIB_DEBUG] 🔧 Processing calibration for pump 1...
[PUMP_CALIB_DEBUG] ✅ Pump 1 calculation: NEW_CALIB = 1.44
[PUMP_CALIB_DEBUG] 🚀 Sending 1 modbus write commands
```

### Kiểm tra Global Variables:
- `configKeyValues`: Chứa `CALIB_ACTUAL_ML_BOM_n` và `CALCULATE_CALIB_BOM_n`
- `holdingRegisterData`: Chứa `HOLDING_SETML_BOM_n` và `HOLDING_CALIB_BOM_n`
- `modbusHoldingRegisters`: Mapping địa chỉ Modbus

## 🎉 Success Indicators

### Frontend:
- ✅ Không có lỗi console
- ✅ API calls thành công (200 status)
- ✅ MQTT messages được nhận
- ✅ UI state updates đúng

### Backend (Node-RED):
- ✅ Debug logs hiển thị processing
- ✅ Modbus write thành công
- ✅ Flags được reset về false

### Hardware:
- ✅ Giá trị calibration mới được ghi xuống board
- ✅ Bơm hoạt động với hệ số mới
