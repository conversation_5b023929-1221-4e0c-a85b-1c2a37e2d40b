import "antd/dist/reset.css";
import "simple-keyboard/build/css/index.css";
import "../style.css";
import React, { useRef, useState, useEffect } from "react";
import Head from "next/head";
import type { AppProps } from "next/app";
import GlobalLayout from "../layouts/global";
import { useRouter } from "next/router";

// check đã log in chưa, nếu chưa thì route qua trang log gin: authentication.tsx
import { useMqttStore } from "../stores/mqttStore";
import {
  DEV_WS_MQTT_DOMAIN,
  // LOCAL_WS_MQTT_DOMAIN,
} from "../services/utilities";
import useUserStore from "../stores/userStore";
import { Button, Modal } from "antd";

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const email = useUserStore((state) => state.email);
  const [isLoading, setIsLoading] = useState(true);

  const checkUserAuthentication = () => {
    const getToken = localStorage.getItem("token") !== null;
    if (getToken && email !== undefined && email !== "") {
      return true;
    }
    return false;
  };
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { connect } = useMqttStore();

  useEffect(() => {
    const authStatus = checkUserAuthentication();
    setIsAuthenticated(authStatus);
    setIsLoading(false);

    if (authStatus) {
      // connect(LOCAL_WS_MQTT_DOMAIN);
      connect(DEV_WS_MQTT_DOMAIN);
      if (router.pathname !== "/vietplants/home") {
        router.push("/vietplants/home");
      }
    } else if (
      router.pathname !== "/user/login" &&
      router.pathname !== "/user/forgot-password"
    ) {
      router.push("/user/login");
    }
  }, [email]);

  const handleCloseApp = () => {
    window.ipc.send("close-app", null);
  };
  const handleMinimizeApp = () => {
    window.ipc.send("minimize-app", null);
  };

  const [openModal, setOpenModal] = useState(false);

  if (isLoading) {
    return <div>...</div>;
  }

  return (
    <React.Fragment>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      {/* render ra layout khi đã log in */}
      {isAuthenticated ? (
        <GlobalLayout>
          <Component {...pageProps} />
        </GlobalLayout>
      ) : (
        <>
          <Component {...pageProps} />
          <Button
            style={{
              position: "fixed",
              top: "10px",
              left: "10px",
              borderColor: "orange",
            }}
            onClick={handleMinimizeApp}
          >
            <p style={{ color: "orange" }}>_</p>
          </Button>
          <Button
            danger
            style={{ position: "fixed", top: "10px", left: "60px" }}
            onClick={() => setOpenModal(true)}
          >
            X
          </Button>
          <Modal
            open={openModal}
            onCancel={() => setOpenModal(false)}
            onOk={() => handleCloseApp()}
            title="Bạn muốn tắt ứng dụng ?"
          ></Modal>
        </>
      )}
    </React.Fragment>
  );
}

export default MyApp;
