import { CSSProperties, FC } from "react";

type AnimateValveT1Props = {
  absolutePosition: CSSProperties;
  valve: any;
};

const AnimateValveT1: FC<AnimateValveT1Props> = ({
  absolutePosition,
  valve,
}) => {
  return (
    <div
      style={{
        position: "absolute",
        zIndex: 100,
        ...absolutePosition,
      }}
    >
      <div style={{ position: "relative" }}>
        <p
          style={{
            position: "absolute",
            top: 52,
            left: -10,
            fontSize: 11,
            color: valve.enable === true ? "#1200DC" : "#797979",
            fontWeight: valve.enable === true ? "bold" : "normal",
          }}
        >
          {valve.id.slice(6)}
        </p>
        <div
          className={`${valve.enable === true ? "route-pipeline" : ""}`}
          style={{
            position: "absolute",
            transform: "rotate(-90deg)",
            top: 5,
            left: -10,
            height: 4,
            width: 28,
            backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
          }}
        ></div>
        <img
          src={
            valve.enable === true
              ? "/scada/valve-open.svg"
              : "/scada/valve-close.svg"
          }
          style={{
            position: "absolute",
            left: 0,
            top: 20,
            height: 24,
            width: 24,
            transform: "rotate(90deg)",
          }}
        />
        <div
          className={`${valve.enable === true ? "route-pipeline" : ""}`}
          style={{
            position: "absolute",
            transform: "rotate(-90deg)",
            left: -10,
            top: 56,
            height: 4,
            width: 28,
            backgroundColor: valve.enable === true ? "#1200DC" : "#797979",
          }}
        ></div>
      </div>
    </div>
  );
};
export default AnimateValveT1;
