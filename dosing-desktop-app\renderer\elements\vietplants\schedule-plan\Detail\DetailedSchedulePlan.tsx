import { FC, useEffect, useState } from "react";
import useSchedulePlanStore, {
  SchedulePlanProps,
} from "../../../../stores/schedulePlanStore";
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Divider,
  Drawer,
  Form,
  Input,
  message,
  Row,
  Select,
} from "antd";
import dayjs from "dayjs";
import { DownOutlined, PlusOutlined, UpOutlined } from "@ant-design/icons";
import ProgramContainer from "../ProgramContainer";
import { updateSchedulePlan } from "../../../../services/schedule";
import CreateProgram from "../Create/CreateProgram";
import DeleteSchedulePlan from "../Update/DeleteSchedulePlan";
import InputTextWithKeyboard from "../../../../components/virtual-input/InputTextWithKeyboard";

const DetailedSchedulePlan: FC<{
  plan: SchedulePlanProps;
  onClose: () => void;
}> = ({ plan, onClose }) => {
  const [form] = Form.useForm();

  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();

  const [sortUp, setSortUp] = useState<boolean>(true);
  const [sortType, setSortType] = useState<string>("action_time");

  const handleSort = (sort_up: boolean, sort_type: string) => {
    if (sort_type === "action_time") {
      plan?.schedules?.sort((a, b) => {
        const today = new Date().toISOString().split("T")[0];
        const startDate = new Date(`${today}T${a.start_time}`);
        const endDate = new Date(`${today}T${b.start_time}`);
        if (sort_up) {
          return startDate.getTime() - endDate.getTime();
        } else {
          return endDate.getTime() - startDate.getTime();
        }
      });
    } else if (sort_type === "program_name") {
      plan?.schedules?.sort((a, b) => {
        if (sort_up) {
          return a.name.localeCompare(b.name);
        } else {
          return b.name.localeCompare(a.name);
        }
      });
    } else if (sort_type === "creation") {
      plan?.schedules?.sort((a, b) => {
        if (sort_up) {
          return dayjs(a.creation).isBefore(dayjs(b.creation)) ? -1 : 1;
        } else {
          return dayjs(a.creation).isBefore(dayjs(b.creation)) ? 1 : -1;
        }
      });
    } else if (sort_type === "modified") {
      plan?.schedules?.sort((a, b) => {
        if (sort_up) {
          return dayjs(a.modified).isBefore(dayjs(b.modified)) ? -1 : 1;
        } else {
          return dayjs(a.modified).isBefore(dayjs(b.modified)) ? 1 : -1;
        }
      });
    }
  };

  useEffect(() => {
    handleSort(sortUp, sortType);
  }, [sortUp, sortType]);

  const onFinish = async (values: any) => {
    const dataToUpdate = {
      name: plan.name,
      label: values.label,
      device_id: plan.device_id,
      start_date: values.start_date,
      end_date: values.end_date,
      enable: plan.enable ? 1 : 0,
    };
    const res = await updateSchedulePlan(dataToUpdate);
    if (res?.statusOK) {
      const updatedPlans = schedulePlans.map((plan) => {
        if (plan.name === dataToUpdate.name) {
          return {
            ...plan,
            label: values.label,
            start_date: values.start_date,
            end_date: values.end_date,
          };
        }
        return plan;
      });
      setSchedulePlans(updatedPlans);
      message.success("Cập nhật kế hoạch thành công");
      form.resetFields();
      console.log(values);
      onClose();
    }
  };

  const [openDrawerToCreateProgram, setOpenDrawerToCreateProgram] =
    useState<boolean>(false);

  return (
    <Form key={plan.name} form={form} style={{ width: "100%" }}>
      <div
        style={{
          zIndex: 100,
          position: "fixed",
          bottom: 24,
          right: 24,
          display: "flex",
          justifyContent: "flex-end",
          gap: 8,
          padding: 8,
          background: "rgba(255, 255, 255, 0.5)",
          borderRadius: 8,
          backdropFilter: "blur(5px)",
          border: "1px solid #ddd",
          boxShadow: "0px 0px 50px 2px rgba(0, 0, 0, 0.25)",
        }}
      >
        <Button onClick={() => onClose()}>Hủy</Button>
        <Button type="primary" onClick={() => onFinish(form.getFieldsValue())}>
          Lưu
        </Button>
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-end",
          gap: 8,
        }}
      >
        <DeleteSchedulePlan plan_id={plan.name} />
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Form.Item
            name="label"
            label="Tên kế hoạch"
            rules={[{ required: true }]}
            layout="vertical"
          >
            {/* <Input
              required
              defaultValue={plan.label}
              style={{ width: "100%" }}
            /> */}
            <InputTextWithKeyboard
              required
              defaultValue={plan.label}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col span={12}>
          <Form.Item
            name="start_date"
            label="Ngày bắt đầu"
            initialValue={dayjs(plan.start_date)}
            rules={[{ required: true }]}
            layout="vertical"
          >
            <DatePicker style={{ width: "100%" }} />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item
            name="end_date"
            label="Ngày kết thúc"
            initialValue={dayjs(plan.end_date)}
            rules={[{ required: true }]}
            layout="vertical"
          >
            <DatePicker style={{ width: "100%" }} />
          </Form.Item>
        </Col>
      </Row>
      <Divider />
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: 8,
        }}
      >
        <p style={{ fontSize: 18, fontWeight: "bold", margin: 0 }}>
          Danh sách chương trình
        </p>
        <div style={{ display: "flex", flexDirection: "row", gap: 8 }}>
          <Button
            type="default"
            onClick={() => setSortUp((prevSortUp) => !prevSortUp)}
            icon={sortUp ? <UpOutlined /> : <DownOutlined />}
          />
          <Select
            style={{ width: "200px" }}
            defaultValue={sortType}
            options={[
              {
                label: "Thời điểm thực hiện",
                value: "action_time",
              },
              {
                label: "Tên chương trình",
                value: "program_name",
              },
              {
                label: "Thời gian tạo",
                value: "creation",
              },
              {
                label: "Thời gian sửa",
                value: "modified",
              },
            ]}
            onChange={(value) => {
              setSortType(value);
            }}
          ></Select>
        </div>
      </div>
      {plan?.schedules?.length === 0 ? (
        <></>
      ) : (
        <Button
          type="link"
          icon={<PlusOutlined />}
          style={{
            borderRadius: 8,
            marginBottom: 8,
            padding: 0,
          }}
          onClick={() => {
            setOpenDrawerToCreateProgram(true);
          }}
        >
          Thêm chương trình
        </Button>
      )}
      <Row gutter={[16, 16]}>
        {plan?.schedules?.length === 0 ? (
          <div
            style={{
              width: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 16,
            }}
          >
            <p style={{ padding: 8, color: "gray", margin: 0 }}>
              *Chưa có chương trình nào được tạo
            </p>
            <Button
              type="link"
              icon={<PlusOutlined />}
              onClick={() => {
                setOpenDrawerToCreateProgram(true);
              }}
            >
              Thêm chương trình
            </Button>
          </div>
        ) : (
          plan?.schedules?.map((program) => (
            <Col span={24}>
              <ProgramContainer
                program={program}
                start_date_of_plan={plan.start_date}
                end_date_of_plan={plan.end_date}
              />
            </Col>
          ))
        )}
      </Row>

      <Drawer
        title="Thêm chương trình"
        open={openDrawerToCreateProgram}
        onClose={() => {
          setOpenDrawerToCreateProgram(false);
        }}
        width={"70%"}
      >
        <CreateProgram
          onClose={() => {
            setOpenDrawerToCreateProgram(false);
          }}
          deviceId={plan.device_id}
          schedulePlanId={plan.name}
          start_date={plan.start_date}
          end_date={plan.end_date}
        />
      </Drawer>

      <div style={{ width: "100%", height: 80 }}></div>
    </Form>
  );
};

export default DetailedSchedulePlan;
